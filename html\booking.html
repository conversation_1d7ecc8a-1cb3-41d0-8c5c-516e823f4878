<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Now - Carles Tourism</title>

    <!-- Custom CSS Dependencies -->
    <link rel="stylesheet" href="../assets/css/icons/icon-system.min.css">
    <link href="../assets/css/frameworks/ui-framework.min.css" rel="stylesheet">
    <link href="../assets/css/components/alert-system.min.css" rel="stylesheet">
    <link href="../assets/css/components/aos-custom.css" rel="stylesheet">
    <link href="../assets/css/components/flatpickr-custom.css" rel="stylesheet">
    <!-- Custom Swiper CSS -->
    <link rel="stylesheet" href="../assets/css/components/swiper-custom.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="../css/booking.css">
    <!-- Standardized Responsive CSS -->
    <link rel="stylesheet" href="../css/responsive-standard.css">
    <style>
        /* Fixed Home Button */
        .fixed-home-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            display: none; /* Hidden by default, shown on mobile */
        }

        .fixed-home-btn a {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background-color: #00a8b5;
            color: white;
            border-radius: 50%;
            text-decoration: none;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .fixed-home-btn a:hover {
            background-color: #008a94;
            transform: scale(1.1);
        }

        .fixed-home-btn i {
            font-size: 1.5rem;
        }

        /* Show fixed home button only on mobile */
        @media (max-width: 991px) {
            .fixed-home-btn {
                display: block;
            }
        }

        /* Style for the Home link in the navbar */
        .nav-link.home-link {
            background-color: #00a8b5;
            color: white !important;
            font-weight: bold;
            border-radius: 5px;
            padding: 8px 15px;
            margin-right: 10px;
            transition: all 0.3s ease;
        }

        .nav-link.home-link:hover {
            background-color: #008a94;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        /* Make sure the home icon is visible */
        .nav-link.home-link i {
            margin-right: 5px;
        }

        /* Ensure the home link is visible on mobile */
        @media (max-width: 991px) {
            .nav-link.home-link {
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 10px auto;
                width: 100%;
                max-width: 200px;
            }
        }

        /* Make navigation links black and bold for better visibility */
        .navbar-nav .nav-link {
            color: #000000 !important;
            font-weight: bold;
            font-size: 16px;
            padding: 8px 15px;
        }

        .navbar-nav .nav-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <!-- Fixed Home Button -->
    <div class="fixed-home-btn">
        <a href="../php/pages/online-booking.php" onclick="clearFormData()" title="Back to Home">
            <i class="fas fa-home"></i>
        </a>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="../php/pages/online-booking.php" onclick="clearFormData()">
                <img src="../img/carleslogomunicipality.png" alt="Carles Municipality Logo" class="logo me-3">
                <img src="../img/timbook-carles-tourism.png" alt="Carles Tourism Logo" class="logo">
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <i class="fas fa-bars"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../php/pages/online-booking.php#about">About</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
        <div class="hero-section">
            <div class="hero-overlay"></div>
            <div class="hero-content">
                <h1>Book Your Island Adventure</h1>
            <p>Experience the beauty of Carles Islands with our boat rentals</p>
            </div>
        </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="booking-wrapper">
            <div class="booking-card">
                <div class="card-accent"></div>

                <!-- Booking Header -->
                <div class="booking-header">
                    <img src="../img/timbook-carles-tourism.png" alt="Carles Tourism Logo" class="booking-logo">
                    <h1 class="booking-title">Book Your Boat</h1>
                    <p class="booking-subtitle">Choose your perfect island adventure</p>
                    <div class="help-section">
                        <div class="help-text">
                            <i class="fas fa-info-circle"></i>
                            <span>Need help? Click the <i class="fas fa-question-circle"></i> icons for more information</span>
                        </div>
                        <div class="estimated-time">
                            <i class="fas fa-clock"></i>
                            <span>Estimated completion time: 5-10 minutes</span>
                        </div>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="progress-container">
                    <div class="progress-bar" id="progressBar" style="width: 0%;"></div>
                </div>

                <!-- Step Indicators -->
                <div class="step-indicators-row">
                    <div class="step active" data-step="1">
                        <div class="step-circle">1</div>
                        <div class="step-label">Personal Info</div>
                    </div>
                    <div class="step" data-step="2">
                        <div class="step-circle">2</div>
                        <div class="step-label">Date and Time</div>
                    </div>
                    <div class="step" data-step="3">
                        <div class="step-circle">3</div>
                        <div class="step-label">Payment</div>
                    </div>
                </div>

                <!-- Booking Form -->
                <form id="bookingForm" method="POST" action="../process/integrated_verification.php">
                    <!-- Hidden Fields -->
                    <input type="hidden" id="selectedBoat" name="selectedBoat" value="AssignedByTourismOffice">
                    <input type="hidden" id="bookingId" name="bookingId" value="">
                    <!-- Add current date as hidden field for Today's Bookings -->
                    <input type="hidden" id="currentDate" name="currentDate" value="">
                    <!-- Add loaded parameter to ensure proper redirection -->
                    <input type="hidden" id="loaded" name="loaded" value="true">

                    <script>
                        // Set the current date for Today's Bookings filter
                        document.addEventListener('DOMContentLoaded', function() {
                            var today = new Date();
                            var dateString = today.getFullYear() + '-' +
                                            String(today.getMonth() + 1).padStart(2, '0') + '-' +
                                            String(today.getDate()).padStart(2, '0');
                            document.getElementById('currentDate').value = dateString;
                            console.log('Set current date to: ' + dateString);

                            // Generate booking ID with current date to ensure it appears in Today's Bookings
                            var random = Math.floor(10000 + Math.random() * 90000);
                            var bookingId = 'BOAT-' + today.getFullYear() +
                                           String(today.getMonth() + 1).padStart(2, '0') +
                                           String(today.getDate()).padStart(2, '0') + '-' + random;
                            document.getElementById('bookingId').value = bookingId;
                            console.log('Generated booking ID: ' + bookingId);
                        });
                    </script>

                <!-- Step 1: Personal Information -->
                    <div class="booking-step active" id="step1">
                        <h3 class="step-title">Personal Information</h3>
                        <div class="form-grid grid-cols-2">
                            <div class="input-field">
                                <label for="firstName">
                                    First Name <i class="fas fa-question-circle tooltip-icon"></i>
                                    <div class="tooltip-content">Enter your first name as it appears on your ID</div>
                                </label>
                                <input type="text" id="firstName" name="firstName" placeholder="Enter your first name" required pattern="[A-Za-z\s]+" title="Please enter only letters and spaces">
                            </div>
                            <div class="input-field">
                                <label for="lastName">
                                    Last Name <i class="fas fa-question-circle tooltip-icon"></i>
                                    <div class="tooltip-content">Enter your last name as it appears on your ID</div>
                                </label>
                                <input type="text" id="lastName" name="lastName" placeholder="Enter your last name" required pattern="[A-Za-z\s]+" title="Please enter only letters and spaces">
                            </div>
                            <div class="input-field">
                                <label for="suffix">
                                    Suffix <i class="fas fa-question-circle tooltip-icon"></i>
                                    <div class="tooltip-content">Optional. Select your name suffix if applicable (e.g., Jr., Sr., II, etc.). Leave as 'No suffix' if none.</div>
                                </label>
                                <select id="suffix" name="suffix">
                                    <option value="">No suffix</option>
                                    <option value="Jr.">Jr.</option>
                                    <option value="Sr.">Sr.</option>
                                    <option value="I">I</option>
                                    <option value="II">II</option>
                                    <option value="III">III</option>
                                    <option value="IV">IV</option>
                                    <option value="V">V</option>
                                </select>
                            </div>
                            <div class="input-field">
                                <label for="age">
                                    Age <i class="fas fa-question-circle tooltip-icon"></i>
                                    <div class="tooltip-content">Enter your age in years. This helps us ensure proper safety measures</div>
                                </label>
                                <input type="number" id="age" name="age" placeholder="Enter your age" required min="1" max="120" title="Please enter a valid age between 1 and 120">
                            </div>
                            <div class="input-field">
                                <label for="sex">
                                    Sex <i class="fas fa-question-circle tooltip-icon"></i>
                                    <div class="tooltip-content">Select your sex.</div>
                                </label>
                                <select id="sex" name="sex" required>
                                    <option value="">Select sex</option>
                                    <option value="Male">Male</option>
                                    <option value="Female">Female</option>
                                </select>
                            </div>
                            <div class="input-field">
                                <label for="contactNumber">
                                    Contact Number <i class="fas fa-question-circle tooltip-icon"></i>
                                    <div class="tooltip-content">Enter your mobile number where we can reach you</div>
                                </label>
                                <input type="tel" id="contactNumber" name="contactNumber" placeholder="09XXXXXXXXX" required maxlength="11" inputmode="numeric" pattern="09[0-9]{9}" title="Please enter a valid 11-digit Philippine mobile number starting with 09">
                            </div>
                            <div class="input-field">
                                <label for="emailAddress">
                                    Email Address <i class="fas fa-question-circle tooltip-icon"></i>
                                    <div class="tooltip-content">Enter your email where we can send booking confirmation</div>
                                </label>
                                <input type="email" id="emailAddress" name="emailAddress" placeholder="Enter your email address" required>
                            </div>
                            <div class="input-field full-width">
                                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                    <label for="completeAddress" style="margin-bottom: 0;">Complete Address</label>
                                    <div style="position: relative; margin-left: 5px;">
                                        <i class="fas fa-question-circle tooltip-icon"></i>
                                        <div class="tooltip-content" style="top: 25px; left: -150px; width: 300px; background-color: #fffbe6; color: #333; padding: 10px 16px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); border: 1px solid #e8e8e8;">
                                            Enter your full address including house number, street, city, and province
                                        </div>
                                    </div>
                                </div>
                                <textarea id="completeAddress" name="completeAddress" placeholder="Enter your complete address" required style="min-height: 100px;"></textarea>
                            </div>
                            <div class="input-field">
                                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                    <label for="emergencyName" style="margin-bottom: 0;">Emergency Contact Name</label>
                                    <div style="position: relative; margin-left: 5px;">
                                        <i class="fas fa-question-circle tooltip-icon"></i>
                                        <div class="tooltip-content" style="top: 25px; left: -150px; width: 300px; background-color: #fffbe6; color: #333; padding: 10px 16px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); border: 1px solid #e8e8e8;">
                                            Enter the name of your emergency contact person
                                        </div>
                                    </div>
                                </div>
                                <input type="text" id="emergencyName" name="emergencyName" placeholder="Enter emergency contact name" required>
                            </div>
                            <div class="input-field">
                                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                    <label for="emergencyNumber" style="margin-bottom: 0;">Emergency Contact Number</label>
                                    <div style="position: relative; margin-left: 5px;">
                                        <i class="fas fa-question-circle tooltip-icon"></i>
                                        <div class="tooltip-content" style="top: 25px; left: -150px; width: 300px; background-color: #fffbe6; color: #333; padding: 10px 16px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); border: 1px solid #e8e8e8;">
                                            Enter the contact number of your emergency contact person
                                        </div>
                                    </div>
                                </div>
                                <input type="tel" id="emergencyNumber" name="emergencyNumber" placeholder="09XXXXXXXXX" required maxlength="11" inputmode="numeric" pattern="09[0-9]{9}" title="Please enter a valid 11-digit Philippine mobile number starting with 09">
                            </div>
                            <div class="input-field">
                                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                    <label for="locationTourDestination" style="margin-bottom: 0;">Tour Destination</label>
                                    <div style="position: relative; margin-left: 5px;">
                                        <i class="fas fa-question-circle tooltip-icon"></i>
                                        <div class="tooltip-content" style="top: 25px; left: -100px; width: 200px; background-color: #fffbe6; color: #333; padding: 10px 16px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); border: 1px solid #e8e8e8;">
                                            Select your desired island destination
                                        </div>
                                    </div>
                                </div>
                                <select id="locationTourDestination" name="locationTourDestination" required>
                                    <option value="">Select destination</option>
                                    <option value="Gigantes Island">Gigantes Island</option>
                                    <option value="Sicogon Island">Sicogon Island</option>
                                    <option value="Tumaquin Island">Tumaquin Island</option>
                                    <option value="Cabugao Gamay Island">Cabugao Gamay Island</option>
                                    <option value="Calagnaan Island">Calagnaan Island</option>
                                </select>
                            </div>
                            <div class="input-field">
                                <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                    <label for="dropOffLocation" style="margin-bottom: 0;">Drop-off Location</label>
                                    <div style="position: relative; margin-left: 5px;">
                                        <i class="fas fa-question-circle tooltip-icon"></i>
                                        <div class="tooltip-content" style="top: 25px; left: -100px; width: 200px; background-color: #fffbe6; color: #333; padding: 10px 16px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); border: 1px solid #e8e8e8;">
                                            Enter your preferred drop-off location
                                        </div>
                                    </div>
                                </div>
                                <input type="text" id="dropOffLocation" name="dropOffLocation" placeholder="Enter drop-off location" required>
                            </div>
                            <div class="input-field">
                                <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                    <label for="numberOfPax" style="margin-bottom: 0;">Number of Pax</label>
                                    <div style="position: relative; margin-left: 5px;">
                                        <i class="fas fa-question-circle tooltip-icon"></i>
                                        <div class="tooltip-content" style="top: 25px; left: 0; width: 200px; background-color: #fffbe6; color: #333; padding: 10px 16px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); border: 1px solid #e8e8e8;">
                                            Maximum of 25 passengers per booking.
                                        </div>
                                    </div>
                                </div>
                                <input type="number" id="numberOfPax" name="numberOfPax" placeholder="Enter number of pax" required min="1" max="25" value="1">
                                <div style="margin-top: 5px; font-size: 0.8rem; color: var(--text-medium);">Total number of passengers</div>
                            </div>
                        </div>
                        <div class="input-field full-width">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <label style="margin-bottom: 0;">Environmental Fee Details</label>
                                <div style="position: relative; margin-left: 5px;">
                                    <i class="fas fa-question-circle tooltip-icon"></i>
                                    <div class="tooltip-content" style="top: 25px; left: -150px; width: 300px; background-color: #fffbe6; color: #333; padding: 10px 16px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.08); border: 1px solid #e8e8e8;">
                                        Environmental fees are collected to help preserve the natural beauty of the islands.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="input-field full-width">
                            <div class="alert alert-info" style="background-color: #e8f4f8; border-left: 4px solid #17a2b8; padding: 10px 15px; margin-bottom: 15px; border-radius: 4px;">
                                <i class="fas fa-info-circle" style="color: #17a2b8; margin-right: 8px;"></i>
                                <strong>Note:</strong> The Tourism Office will arrange the boat for you. You don't need to book a boat separately.
                            </div>
                        </div>
                        <div class="input-field full-width">
                            <div class="environmental-fee-section">
                                <div class="environmental-fee-header">
                                    <h4>Environmental Fee Rates</h4>
                                    <div class="fee-info">
                                        <span>Regular Adults (13+ years old)</span>
                                        <span>₱75.00</span>
                                    </div>
                                    <div class="fee-info">
                                        <span>Senior Citizens/PWD</span>
                                        <span>₱60.00</span>
                                    </div>
                                    <div class="fee-info">
                                        <span>Children (12 years old and below)</span>
                                        <span>₱60.00</span>
                                    </div>
                                    <div class="fee-info">
                                        <span>Infants (5 years old and below)</span>
                                        <span>Free</span>
                                    </div>
                                </div>

                            <div class="group-fee-inputs">
                                <div class="fee-input-group">
                                    <label for="regularPax">Adults (13+) - ₱75.00 each</label>
                                    <input type="number" id="regularPax" name="regularPax" min="0" max="25" value="0" onchange="calculateTotalFees()">
                                </div>
                                <div class="fee-input-group">
                                    <label for="discountedPax">Senior Citizens/PWD - ₱60.00 each</label>
                                    <input type="number" id="discountedPax" name="discountedPax" min="0" max="25" value="0" onchange="calculateTotalFees()">
                                </div>
                                <div class="fee-input-group">
                                    <label for="childrenPax">Children (12 & below) - ₱60.00 each</label>
                                    <input type="number" id="childrenPax" name="childrenPax" min="0" max="25" value="0" onchange="calculateTotalFees()">
                                </div>
                                <div class="fee-input-group">
                                    <label for="infantsPax">Infants (0-5) - Free</label>
                                    <input type="number" id="infantsPax" name="infantsPax" min="0" max="25" value="0" onchange="calculateTotalFees()">
                                </div>
                            </div>

                            <div class="total-fee-display">
                                <p>Total Environmental Fee: <span id="totalEnvironmentalFee">₱0.00</span></p>
                                <input type="hidden" id="totalEnvironmentalFeeValue" name="totalEnvironmentalFee" value="0">
                            </div>
                            </div>
                        </div>

                        <!-- Booking Information section removed as requested -->

                        <div class="button-group">
                            <button type="button" class="btn-next" onclick="validateAndProceed(1)">Proceed to Date and Time</button>
                        </div>

                        <script>
                            function validateAndProceed(step) {
                                // Simply call the nextStep function directly
                                // The validateStep function in booking.js will handle the validation
                                nextStep(step);
                            }
                        </script>
                    </div>

                    <!-- Step 2: Date & Time -->
                    <div class="booking-step" id="step2">
                        <h3 class="step-title">Choose Date and Time</h3>
                        <div class="date-selection">
                            <div class="form-grid">
                                <div class="input-field">
                                    <label for="startDate">Start Date</label>
                                    <input type="text" id="startDate" name="startDate" class="datepicker" required>
                                </div>
                                <div class="input-field" style="max-width:100%;flex:1 1 0;">
                                    <label for="endDate">End Date</label>
                                    <input type="text" id="endDate" name="endDate" class="datepicker" required>
                                </div>
                            </div>
                            <div class="price-summary">
                                <h4>Tour Details</h4>
                                <div class="price-row">
                                    <span>Start Date</span>
                                    <span id="startDateTime">Not selected</span>
                                </div>
                                <div class="price-row">
                                    <span>End Date</span>
                                    <span id="endDateTime">Not selected</span>
                                </div>
                                <div class="price-row">
                                    <span>Booking Time</span>
                                    <span id="bookingTime">March 22, 2025 at 3:30:01 AM</span>
                                </div>
                            </div>
                        </div>
                        <div class="button-group">
                            <button type="button" class="btn-prev" onclick="prevStep(2)">Back to Personal Info</button>
                            <button type="button" class="btn-next" onclick="goToPaymentStep()">Proceed to Payment</button>
                        </div>

                        <script>
                            // Simple function to go directly to payment step
                            function goToPaymentStep() {
                                // Basic date validation
                                const startDate = document.getElementById('startDate').value;
                                const endDate = document.getElementById('endDate').value;

                                if (!startDate || !endDate) {
                                    alert('Please select both start and end dates.');
                                    return;
                                }

                                // Show step 3 (payment)
                                document.querySelector('.booking-step.active').classList.remove('active');
                                document.getElementById('step3').classList.add('active');

                                // Update progress bar
                                document.getElementById('progressBar').style.width = '100%';

                                // Update booking summary
                                if (typeof updateBookingSummary === 'function') {
                                    updateBookingSummary();
                                }
                            }
                        </script>
                    </div>

                <!-- Step 3: Payment -->
                <div class="booking-step" id="step3">
                    <h3 class="step-title">Payment Details</h3>

                    <div class="payment-section-grid">
                        <div class="booking-summary">
                            <h4>Booking Summary</h4>
                            <div class="summary-details">
                                <div class="price-row" id="summaryEnvironmentalFee">
                                    <span>Environmental Fee:</span>
                                    <span id="summaryEnvFeeValue">₱0.00</span>
                                </div>
                                <div class="price-row">
                                    <span>First Name:</span>
                                    <span id="summaryFirstName">Not provided</span>
                                </div>
                                <div class="price-row">
                                    <span>Last Name:</span>
                                    <span id="summaryLastName">Not provided</span>
                                </div>
                                <div class="price-row">
                                    <span>Age:</span>
                                    <span id="summaryAge">Not provided</span>
                                </div>
                                <div class="price-row">
                                    <span>Sex:</span>
                                    <span id="summarySex">Not provided</span>
                                </div>
                                <div class="price-row">
                                    <span>Contact Number:</span>
                                    <span id="summaryContact">Not provided</span>
                                </div>
                                <div class="price-row">
                                    <span>Email:</span>
                                    <span id="summaryEmail">Not provided</span>
                                </div>
                                <div class="price-row">
                                    <span>Address:</span>
                                    <span id="summaryAddress">Not provided</span>
                                </div>
                                <div class="price-row">
                                    <span>Emergency Contact:</span>
                                    <span id="summaryEmergencyContact">Not provided</span>
                                </div>
                                <div class="price-row">
                                    <span>Tour Destination:</span>
                                    <span id="summaryDestination">Not provided</span>
                                </div>
                                <div class="price-row">
                                    <span>Drop-off Location:</span>
                                    <span id="summaryDropOff">Not provided</span>
                                </div>
                                <div class="price-row">
                                    <span>Number of Pax:</span>
                                    <span id="summaryPax">Not provided</span>
                                </div>
                                <div class="price-row">
                                    <span>Start Date:</span>
                                    <span id="summaryStartDate">Not provided</span>
                                </div>
                                <div class="price-row">
                                    <span>End Date:</span>
                                    <span id="summaryEndDate">Not provided</span>
                                </div>
                                <div class="price-row">
                                    <span>Booking Time:</span>
                                    <span id="summaryBookingTime">Not provided</span>
                                </div>
                                <div class="price-row" id="summaryPaymentMethod">
                                    <span>Payment Method:</span>
                                    <span id="summaryPaymentValue">Not selected</span>
                                </div>
                                <!-- Boat and Booking ID information removed as requested -->
                                <div class="price-row total">
                                    <span>Total:</span>
                                    <span id="totalPrice">₱0.00</span>
                                </div>
                            </div>
                        </div>
                        <div class="payment-methods">
                            <h4>Payment Method</h4>
                            <div class="payment-options">
                                <div class="payment-option">
                                    <input type="radio" id="gcash" name="paymentMethod" value="GCash" required>
                                    <label for="gcash">
                                        <div class="gcash-icon">G</div>
                                        <span>GCash</span>
                                    </label>
                                </div>
                                <div class="payment-option">
                                    <input type="radio" id="manual" name="paymentMethod" value="Manual Payment">
                                    <label for="manual">
                                        <i class="fas fa-hand-holding-usd"></i>
                                        <span>Manual Payment</span>
                                    </label>
                                </div>
                            </div>

                            <!-- GCash Payment Details -->
                            <div id="gcashDetails" class="payment-details" style="display: none;">
                                <div class="gcash-info">
                                    <h5>GCash Payment Details</h5>
                                    <div class="gcash-account">
                                        <p><strong>Account Name:</strong> Carles Tourism Office</p>
                                        <p><strong>GCash Number:</strong> 0945 799 3491</p>
                                    </div>
                                    <div class="gcash-qr">
                                        <img src="../img/gcash-qr.png" alt="GCash QR Code" style="max-width: 200px; margin: 20px auto; display: block;">
                                        <p class="text-center">Scan QR Code to Pay</p>
                                    </div>
                                    <div class="payment-instructions">
                                        <h6>Payment Instructions:</h6>
                                        <ol>
                                            <li>Open your GCash app</li>
                                            <li>Scan the QR code above</li>
                                            <li>Enter the total amount</li>
                                            <li>Add your booking reference in the description</li>
                                            <li>Complete the payment</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>

                            <!-- Manual Payment Details -->
                            <div id="manualDetails" class="payment-details" style="display: none;">
                                <div class="manual-info">
                                    <h5>Manual Payment Details</h5>
                                    <div class="payment-location">
                                        <p><strong>Payment Location: </strong>Bancal Port, Carles, Iloilo, 5019</p>
                                        <p><strong>Address: </strong> Bancal, Carles, Iloilo</p>
                                        <p><strong>Contact Number: </strong> 0945 799 3491</p>
                                        <p><strong>Email: </strong><EMAIL></p>
                                        <p><strong>Office Hours: </strong> Monday - Friday, 8:00 AM - 5:00 PM</p>
                                    </div>
                                    <div class="payment-instructions">
                                        <h6>Payment Instructions:</h6>
                                        <ol>
                                            <li>Visit the Carles Tourism Office</li>
                                            <li>Present your booking reference</li>
                                            <li>Pay the total amount in cash</li>
                                            <li>Receive your official receipt</li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="button-group" id="step3-buttons">
                        <button type="button" class="btn-prev" onclick="prevStep(3)">Back to Date and Time</button>
                        <button type="submit" class="btn-submit">Complete Booking</button>
                    </div>

                    <!-- Add hidden field for total amount -->
                    <input type="hidden" id="total" name="total" value="0">

                    <script>
                        // Set up form submission
                        document.getElementById('bookingForm').addEventListener('submit', function(e) {
                            // Prevent the default form submission
                            e.preventDefault();

                            // Make sure all required fields are filled
                            var requiredFields = document.querySelectorAll('[required]');
                            var allFilled = true;

                            requiredFields.forEach(function(field) {
                                if (!field.value) {
                                    allFilled = false;
                                    field.classList.add('error');
                                } else {
                                    field.classList.remove('error');
                                }
                            });

                            if (!allFilled) {
                                alert('Please fill in all required fields');
                                return;
                            }

                            // Set the total amount
                            var envFeeText = document.getElementById('summaryEnvFeeValue').textContent.replace('₱', '').replace(',', '') || '0.00';
                            var envFee = parseFloat(envFeeText) || 0;
                            document.getElementById('total').value = envFee;
                            console.log('Setting total amount to:', envFee);

                            // Force the current date for today's bookings
                            var today = new Date();
                            var dateString = today.getFullYear() + '-' +
                                            String(today.getMonth() + 1).padStart(2, '0') + '-' +
                                            String(today.getDate()).padStart(2, '0');
                            document.getElementById('currentDate').value = dateString;

                            // Set the start date to today to ensure it appears in Today's Bookings
                            document.getElementById('startDate').value = dateString;

                            // Submit the form
                            console.log('Submitting booking form to send_verification_email.php...');
                            this.submit();
                        });
                    </script>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Back to Home Link - RAMOS -->
<div class="back-to-home">
    <a href="../php/pages/online-booking.php" onclick="clearFormData()">
        <i class="fas fa-home"></i>
        Back to Home
    </a>
</div>

<style>
    /* Enhanced Back to Home Link */
    .back-to-home {
        text-align: center;
        margin: 30px 0;
    }

    .back-to-home a {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        background-color: #00a8b5;
        color: white;
        padding: 12px 25px;
        border-radius: 30px;
        text-decoration: none;
        font-weight: bold;
        transition: all 0.3s ease;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .back-to-home a:hover {
        background-color: #008a94;
        transform: translateY(-3px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .back-to-home i {
        margin-right: 10px;
        font-size: 1.2rem;
    }

    /* Enhanced Payment Options Styling */
    .payment-options {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
    }

    .payment-option {
        flex: 1;
    }

    .payment-option input[type="radio"] {
        display: none;
    }

    .payment-option label {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 15px;
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .payment-option input[type="radio"]:checked + label {
        border-color: #00a8b5;
        background-color: #e8f6ef;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .payment-option label:hover {
        border-color: #00a8b5;
        transform: translateY(-2px);
    }

    .gcash-icon {
        width: 40px;
        height: 40px;
        background-color: #00aef0;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 20px;
        margin-bottom: 8px;
    }

    .payment-option i.fas {
        font-size: 24px;
        color: #00a8b5;
        margin-bottom: 8px;
    }

    .payment-details {
        background-color: #f9f9f9;
        border-radius: 8px;
        padding: 20px;
        margin-top: 15px;
        border: 1px solid #e0e0e0;
    }

    .payment-location p, .gcash-account p {
        margin-bottom: 8px;
    }

    .payment-instructions ol {
        padding-left: 20px;
    }

    .payment-instructions li {
        margin-bottom: 5px;
    }
</style>

<!-- Custom JavaScript Dependencies -->
<script src="../assets/js/frameworks/dom-library.min.js"></script>
<script src="../assets/js/frameworks/ui-framework.min.js"></script>
<script src="../assets/js/components/alert-system.min.js"></script>
<script src="../assets/js/components/aos-custom.js"></script>
<script src="../assets/js/components/flatpickr-custom.js"></script>
<script src="../assets/js/components/swiper-custom.js"></script>
<script src="../js/booking.js"></script>
<script>
// Mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (navbarToggler && navbarCollapse) {
        navbarToggler.addEventListener('click', function() {
            // Toggle the 'show' class on the navbar collapse
            navbarCollapse.classList.toggle('show');

            // Toggle the 'expanded' class on the toggler for styling
            this.classList.toggle('expanded');

            // Update aria-expanded attribute
            const expanded = navbarCollapse.classList.contains('show');
            this.setAttribute('aria-expanded', expanded);
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(event) {
            if (!navbarToggler.contains(event.target) &&
                !navbarCollapse.contains(event.target) &&
                navbarCollapse.classList.contains('show')) {
                navbarCollapse.classList.remove('show');
                navbarToggler.classList.remove('expanded');
                navbarToggler.setAttribute('aria-expanded', 'false');
            }
        });

        // Close menu when clicking on a nav link
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (navbarCollapse.classList.contains('show')) {
                    navbarCollapse.classList.remove('show');
                    navbarToggler.classList.remove('expanded');
                    navbarToggler.setAttribute('aria-expanded', 'false');
                }
            });
        });
    }

    // Payment method selection
    const gcashRadio = document.getElementById('gcash');
    const manualRadio = document.getElementById('manual');
    const gcashDetails = document.getElementById('gcashDetails');
    const manualDetails = document.getElementById('manualDetails');

    if (gcashRadio && manualRadio) {
        // Set GCash as default selected payment method
        gcashRadio.checked = true;
        gcashDetails.style.display = 'block';

        // Update payment method in summary with default selection
        document.getElementById('summaryPaymentValue').textContent = gcashRadio.value;

        gcashRadio.addEventListener('change', function() {
            if (this.checked) {
                gcashDetails.style.display = 'block';
                manualDetails.style.display = 'none';
                // Update payment method in summary
                document.getElementById('summaryPaymentValue').textContent = this.value;
            }
        });

        manualRadio.addEventListener('change', function() {
            if (this.checked) {
                manualDetails.style.display = 'block';
                gcashDetails.style.display = 'none';
                // Update payment method in summary
                document.getElementById('summaryPaymentValue').textContent = this.value;
            }
        });
    }
});
</script>
</body>
</html>