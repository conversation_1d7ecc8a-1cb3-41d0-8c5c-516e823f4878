# External Dependencies Analysis - Online Booking Reservation System

## Overview
This document lists all external plugins, libraries, frameworks, and dependencies that need to be removed and replaced with custom code for thesis defense requirements.

## ADMIN SYSTEM DEPENDENCIES

### 1. AdminLTE Framework (MAJOR)
**Location**: `admin-system/dashboard/dist/`
**Files**: 
- `dist/css/adminlte.min.css` - Main AdminLTE stylesheet
- `dist/js/adminlte.min.js` - Main AdminLTE JavaScript
- `dist/css/alt/adminlte.*.css` - Alternative AdminLTE components
**Usage**: Complete admin dashboard template, layout, components
**Impact**: HIGH - Entire admin interface design and functionality

### 2. Bootstrap Framework (MAJOR)
**Location**: `admin-system/dashboard/plugins/bootstrap/`
**Files**:
- `plugins/bootstrap/css/bootstrap.min.css`
- `plugins/bootstrap/js/bootstrap.min.js`
- `plugins/bootstrap/js/bootstrap.bundle.min.js`
**Usage**: Grid system, components, responsive design
**Impact**: HIGH - Layout and responsive design

### 3. jQuery Library (MAJOR)
**Location**: `admin-system/dashboard/plugins/jquery/`
**Files**:
- `plugins/jquery/jquery.min.js`
- `js/jquery-2.2.3.min.js`
- `plugins/jquery-ui/` (jQuery UI)
**Usage**: DOM manipulation, AJAX, event handling
**Impact**: HIGH - Most JavaScript functionality depends on this

### 4. Font Awesome Icons (MAJOR)
**Location**: `admin-system/dashboard/plugins/fontawesome-free/`
**Files**:
- `plugins/fontawesome-free/css/all.min.css`
- `plugins/fontawesome-free/css/solid.min.css`
- `plugins/fontawesome-free/webfonts/` (font files)
**Usage**: Icons throughout admin interface
**Impact**: MEDIUM - Visual icons and interface elements

### 5. DataTables Plugin (MAJOR)
**Location**: `admin-system/dashboard/plugins/datatables*/`
**Files**:
- `plugins/datatables/` - Core DataTables
- `plugins/datatables-bs4/` - Bootstrap 4 integration
- `plugins/datatables-buttons/` - Export buttons
- `plugins/datatables-responsive/` - Responsive tables
**Usage**: Advanced table functionality (sorting, filtering, pagination)
**Impact**: HIGH - All admin tables use this

### 6. Chart.js (MEDIUM)
**Location**: `admin-system/dashboard/plugins/chart.js/`
**Usage**: Dashboard charts and graphs
**Impact**: MEDIUM - Dashboard analytics

### 7. SweetAlert2 (MEDIUM)
**Location**: `admin-system/dashboard/plugins/sweetalert2/`
**Usage**: Alert dialogs and confirmations
**Impact**: MEDIUM - User notifications

### 8. Other AdminLTE Plugins (VARIOUS)
**Locations**: `admin-system/dashboard/plugins/`
- `bootstrap-colorpicker/` - Color picker
- `bootstrap-slider/` - Range sliders
- `daterangepicker/` - Date range picker
- `select2/` - Enhanced select boxes
- `summernote/` - Rich text editor
- `tempusdominus-bootstrap-4/` - Date/time picker
- `toastr/` - Toast notifications
- `moment/` - Date manipulation
- `fullcalendar/` - Calendar component

## MAIN WEBSITE DEPENDENCIES

### 1. Bootstrap CSS Framework (MAJOR)
**Location**: CDN Links in HTML files
**Files**: 
- `https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css`
- `https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js`
**Usage**: Grid system, components, responsive design
**Impact**: HIGH - Main website layout

### 2. jQuery Library (MAJOR)
**Location**: CDN Links
**Files**: `https://code.jquery.com/jquery-3.6.0.min.js`
**Usage**: DOM manipulation, AJAX calls
**Impact**: HIGH - Main website JavaScript functionality

### 3. Font Awesome Icons (MAJOR)
**Location**: CDN Links
**Files**: `https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css`
**Usage**: Icons throughout main website
**Impact**: MEDIUM - Visual elements

### 4. Google Fonts (MEDIUM)
**Location**: CDN Links
**Files**: `https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap`
**Usage**: Typography
**Impact**: LOW - Can be replaced with web-safe fonts

### 5. Swiper.js (MEDIUM)
**Location**: CDN Links
**Files**: `https://unpkg.com/swiper/swiper-bundle.min.css`
**Usage**: Image carousels and sliders
**Impact**: MEDIUM - Gallery functionality

### 6. SweetAlert2 (MEDIUM)
**Location**: CDN Links
**Files**: `https://cdn.jsdelivr.net/npm/sweetalert2@11.15.10/dist/sweetalert2.all.min.js`
**Usage**: Alert dialogs
**Impact**: MEDIUM - User notifications

### 7. AOS (Animate On Scroll) (LOW)
**Location**: CDN Links
**Files**: `https://unpkg.com/aos@2.3.1/dist/aos.js`
**Usage**: Scroll animations
**Impact**: LOW - Visual enhancements

### 8. Flatpickr (LOW)
**Location**: CDN Links
**Files**: `https://cdn.jsdelivr.net/npm/flatpickr`
**Usage**: Date picker
**Impact**: LOW - Date selection

## REPLACEMENT STRATEGY

### Phase 1: Core Framework Replacement
1. Replace Bootstrap with custom CSS grid and components
2. Replace jQuery with vanilla JavaScript
3. Replace AdminLTE with custom admin design

### Phase 2: Component Replacement
1. Replace DataTables with custom table functionality
2. Replace Font Awesome with custom SVG icons
3. Replace SweetAlert2 with custom modals

### Phase 3: Enhancement Replacement
1. Replace Swiper with custom carousel
2. Replace Chart.js with custom charts
3. Replace form plugins with custom components

## ESTIMATED IMPACT
- **Files to be modified**: 50+ files
- **External dependencies to remove**: 20+ libraries
- **Custom code to write**: ~5000+ lines
- **Testing required**: Complete system functionality

## NEXT STEPS
1. Create custom CSS framework
2. Build custom JavaScript library
3. Design new admin interface
4. Implement custom components
5. Test all functionality
