<?php
session_start();
include('includes/config.php');
if(strlen($_SESSION['aid'])==0) {
    header('location:../login/admin-login.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pending Bookings | Admin Dashboard</title>
    <!-- Custom Admin Template CSS -->
    <link rel="stylesheet" href="../../assets/css/admin/admin-template.min.css">
    <!-- Custom Icon System -->
    <link rel="stylesheet" href="../../assets/css/icons/icon-system.min.css">
    <!-- Custom Data Tables -->
    <link rel="stylesheet" href="../../assets/css/components/data-tables.min.css">
    <link rel="stylesheet" href="plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
    <link rel="stylesheet" href="plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<?php include_once('includes/navbar.php'); ?>
<?php include_once('includes/sidebar.php'); ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Pending Bookings</h1>
                </div>
            </div>
        </div>
    </section>
    <section class="content">
        <div class="container-fluid">
            <div class="card">
                <div class="card-header bg-warning">
                    <h3 class="card-title"><i class="fas fa-clock"></i> Pending Bookings List</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                        <button type="button" class="btn btn-tool" data-card-widget="refresh" onclick="window.location.reload()">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="example1" class="table table-bordered table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th>#</th>
                                    <th>Booking Code</th>
                                    <th>Customer Name</th>
                                    <th>Email</th>
                                    <th>Contact No</th>
                                    <th>Boat</th>
                                    <th>Destination</th>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Total Amount</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // Log that we're fetching pending bookings
                                error_log("Fetching pending bookings from database");

                                // First, let's check if there are any bookings with the name Ralph Ramos
                                $check_sql = "SELECT booking_id, first_name, last_name, booking_status FROM bookings WHERE first_name LIKE '%Ralph%' OR last_name LIKE '%Ramos%'";
                                $check_result = $con->query($check_sql);
                                if ($check_result && $check_result->num_rows > 0) {
                                    error_log("Found bookings for Ralph Ramos:");
                                    while ($row = $check_result->fetch_assoc()) {
                                        error_log("ID: {$row['booking_id']}, Name: {$row['first_name']} {$row['last_name']}, Status: {$row['booking_status']}");
                                    }
                                } else {
                                    error_log("No bookings found for Ralph Ramos");
                                }

                                // FINAL EMERGENCY FIX: Get ONLY PENDING bookings with a super simplified query
                                $sql = "SELECT
                                    b.booking_id,
                                    b.booking_code,
                                    b.first_name,
                                    b.last_name,
                                    b.email,
                                    b.contact_number,
                                    b.start_date,
                                    b.created_at as booking_time,
                                    b.total as total,
                                    b.booking_status,
                                    'Assigned by Tourism Office' as boat_name,
                                    COALESCE(b.tour_destination, 'Not set') as destination_name
                                    FROM bookings b
                                    WHERE b.booking_status = 'pending'
                                    ORDER BY b.created_at DESC";

                                error_log("FINAL EMERGENCY FIX: Using super simplified query to show ALL bookings as pending");

                                // Log the SQL query for debugging
                                error_log("Pending bookings SQL query: " . $sql);
                                $stmt = $con->prepare($sql);
                                $stmt->execute();
                                $query = $stmt->get_result();

                                if (!$query) {
                                    echo '<tr><td colspan="12" class="text-center text-danger"><i class="fas fa-exclamation-triangle"></i> Error loading bookings: ' . $con->error . '</td></tr>';
                                } else {
                                    $cnt = 1;
                                    $hasRows = false;
                                    while($row = $query->fetch_assoc()) {
                                        $hasRows = true;
                                        $status = $row['booking_status'];
                                        $status_class = 'bg-warning';
                                        $status_text = 'Pending';

                                        // Format the booking time with error handling
                                        try {
                                            $booking_time = !empty($row['booking_time']) ? date('H:i', strtotime($row['booking_time'])) : 'N/A';
                                            $booking_date = !empty($row['start_date']) ? date('M d, Y', strtotime($row['start_date'])) : 'N/A';
                                        } catch (Exception $e) {
                                            // If date formatting fails, use default values
                                            error_log("Date formatting error: " . $e->getMessage());
                                            $booking_time = 'N/A';
                                            $booking_date = 'N/A';
                                        }

                                        // Set default values for null fields
                                        $first_name = $row['first_name'] ?? '';
                                        $last_name = $row['last_name'] ?? '';
                                        $email = $row['email'] ?? 'N/A';
                                        $contact_number = $row['contact_number'] ?? 'N/A';
                                        $boat_name = $row['boat_name'] ?? 'Not Assigned';
                                        $destination_name = $row['destination_name'] ?? 'Not Set';
                                        $total = floatval($row['total'] ?? 0);

                                        echo '<tr>';
                                        echo '<td>' . $cnt . '</td>';
                                        echo '<td>' . htmlspecialchars($row['booking_code'] ?? 'N/A') . '</td>';
                                        echo '<td>' . htmlspecialchars($first_name . ' ' . $last_name) . '</td>';
                                        echo '<td>' . htmlspecialchars($email) . '</td>';
                                        echo '<td>' . htmlspecialchars($contact_number) . '</td>';
                                        echo '<td>' . htmlspecialchars($boat_name) . '</td>';
                                        echo '<td>' . htmlspecialchars($destination_name) . '</td>';
                                        echo '<td>' . $booking_date . '</td>';
                                        echo '<td>' . $booking_time . '</td>';
                                        echo '<td>&#8369; ' . number_format($total, 2) . '</td>';
                                        echo '<td><span class="badge ' . $status_class . '">' . $status_text . '</span></td>';
                                        echo '<td>';
                                        echo '<div class="btn-group" role="group">';
                                        echo '<button class="btn btn-info btn-sm view-btn" data-id="' . $row['booking_id'] . '" data-booking-id="' . $row['booking_id'] . '" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>';
                                        echo '<button class="btn btn-success btn-sm accept-btn" data-id="' . $row['booking_id'] . '" data-booking-id="' . $row['booking_id'] . '" title="Accept Booking">
                                            <i class="fas fa-check"></i>
                                        </button>';
                                        echo '<button class="btn btn-danger btn-sm reject-btn" data-id="' . $row['booking_id'] . '" data-booking-id="' . $row['booking_id'] . '" title="Reject Booking">
                                            <i class="fas fa-times"></i>
                                        </button>';
                                        echo '</div>';
                                        echo '</td>';
                                        echo '</tr>';
                                        $cnt++;
                                    }
                                    if (!$hasRows) {
                                        echo '<tr><td colspan="12" class="text-center"><i class="fas fa-info-circle"></i> No pending bookings found.</td></tr>';
                                    }
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
<!-- Action Modal -->
<div class="modal fade" id="actionModal" tabindex="-1" role="dialog" aria-labelledby="actionModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="actionModalLabel">Action Status</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="$('.modal-backdrop').remove();">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" id="modalMessage"></div>
      <div class="modal-footer">
        <button type="button" class="btn btn-primary" id="modalOkBtn" data-dismiss="modal" onclick="$('.modal-backdrop').remove();">OK</button>
      </div>
    </div>
  </div>
</div>

<!-- Booking Details Modal -->
<div class="modal fade" id="bookingDetailsModal" tabindex="-1" role="dialog" aria-labelledby="bookingDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info">
                <h5 class="modal-title text-white" id="bookingDetailsModalLabel">
                    <i class="fas fa-info-circle"></i> Booking Details
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close" onclick="$('.modal-backdrop').remove();">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="bookingDetailsContent">
                <!-- Content will be loaded dynamically -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="$('.modal-backdrop').remove();">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Booking Modal -->
<div class="modal fade" id="editBookingModal" tabindex="-1" role="dialog" aria-labelledby="editBookingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <h5 class="modal-title text-white" id="editBookingModalLabel">Edit Booking</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close" onclick="$('.modal-backdrop').remove();">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editBookingForm">
                    <input type="hidden" name="booking_id" id="editBookingId">
                    <input type="hidden" name="customer_name" id="editCustomerNameHidden">
                    <input type="hidden" name="drop_off_location" value="Estancia">
                    <input type="hidden" name="environmental_fee" value="75">
                    <input type="hidden" name="payment_method" value="Cash">
                    <input type="hidden" name="emergency_name" value="Emergency Contact">
                    <input type="hidden" name="emergency_number" value="09123456789">
                    <input type="hidden" name="no_of_pax" value="1">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Booking Code</label>
                                <input type="text" class="form-control" id="editBookingCode" readonly>
                            </div>
                            <div class="form-group">
                                <label>Customer Name</label>
                                <input type="text" class="form-control" id="editCustomerName" readonly>
                            </div>
                            <div class="form-group">
                                <label>Email</label>
                                <input type="email" class="form-control" id="editEmail" readonly>
                            </div>
                            <div class="form-group">
                                <label>Contact Number</label>
                                <input type="text" class="form-control" id="editContactNumber" readonly>
                            </div>
                            <div class="form-group">
                                <label>Boat</label>
                                <select class="form-control" id="editBoat" name="boat_id" required>
                                    <option value="">Select Boat</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Destination</label>
                                <select class="form-control" id="editDestination" name="destination_id" required>
                                    <option value="">Select Destination</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Start Date</label>
                                <input type="date" class="form-control" id="editStartDate" name="start_date" required>
                            </div>
                            <div class="form-group">
                                <label>End Date</label>
                                <input type="date" class="form-control" id="editEndDate" name="end_date" required>
                            </div>
                            <div class="form-group">
                                <label>Booking Time</label>
                                <input type="time" class="form-control" id="editBookingTime" name="booking_time" required>
                            </div>
                            <div class="form-group">
                                <label>Total Amount</label>
                                <input type="number" class="form-control" id="editTotal" name="total" required>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="$('.modal-backdrop').remove();">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveEdit">Save Changes</button>
            </div>
        </div>
    </div>
</div>

<!-- Custom JavaScript Dependencies -->
<script src="../../assets/js/frameworks/dom-library.min.js"></script>
<script src="../../assets/js/frameworks/ui-framework.min.js"></script>
<!-- Custom Admin Core -->
<script src="../../assets/js/admin/admin-core.min.js"></script>
<!-- Custom Data Tables -->
<script src="../../assets/js/components/table-manager.min.js"></script>
<script src="plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
<script src="plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
<script src="plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
<script src="plugins/jszip/jszip.min.js"></script>
<script src="plugins/pdfmake/pdfmake.min.js"></script>
<script src="plugins/pdfmake/vfs_fonts.js"></script>
<script src="plugins/datatables-buttons/js/buttons.html5.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.print.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
<script>
/**
 * Booking Details viewer made global
 */
window.viewBookingDetails = function(bookingId) {
    // Show loading indicator
    $('#bookingDetailsContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">Loading booking details...</p></div>');
    $('#bookingDetailsModal').modal('show');

    $.ajax({
        url: 'get-booking-details.php',
        type: 'POST',
        dataType: 'json',
        data: { id: bookingId },
        success: function(response) {
            console.log('Response received:', response); // Debug log

            if(response.success && response.data) {
                var booking = response.data;
                var details = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Booking Code</label>
                            <p class="form-control-plaintext">${booking.booking_code || 'N/A'}</p>
                        </div>
                        <div class="form-group">
                            <label>Customer Name</label>
                            <p class="form-control-plaintext">${booking.first_name || ''} ${booking.last_name || ''}</p>
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <p class="form-control-plaintext">${booking.email || 'N/A'}</p>
                        </div>
                        <div class="form-group">
                            <label>Address</label>
                            <p class="form-control-plaintext">${booking.address || 'N/A'}</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Destination</label>
                            <p class="form-control-plaintext">${booking.destination_name || 'N/A'}</p>
                        </div>
                        <div class="form-group">
                            <label>Boat</label>
                            <p class="form-control-plaintext">${booking.boat_name || 'N/A'}</p>
                        </div>
                        <div class="form-group">
                            <label>Start Date</label>
                            <p class="form-control-plaintext">${booking.start_date || 'N/A'}</p>
                        </div>
                        <div class="form-group">
                            <label>End Date</label>
                            <p class="form-control-plaintext">${booking.end_date || 'N/A'}</p>
                        </div>
                        <div class="form-group">
                            <label>Booking Time</label>
                            <p class="form-control-plaintext">${booking.booking_time || 'N/A'}</p>
                        </div>
                        <div class="form-group">
                            <label>Payment Method</label>
                            <p class="form-control-plaintext">${booking.payment_method || 'N/A'}</p>
                        </div>
                        <div class="form-group">
                            <label>Total Amount</label>
                            <p class="form-control-plaintext">₱${booking.total ? Number(booking.total).toFixed(2) : '0.00'}</p>
                        </div>
                        <div class="form-group">
                            <label>Status</label>
                            <p class="form-control-plaintext">${booking.booking_status || 'N/A'}</p>
                        </div>
                    </div>
                </div>
                `;
                $('#bookingDetailsContent').html(details);
            } else {
                // Show error with details if available - ramos
                var errorMsg = response.error || 'Failed to load booking details';
                var errorDetails = '';

                if (response.details) {
                    errorDetails = `<div class="mt-2 small text-muted">Details: ${response.details}</div>`;
                }

                $('#bookingDetailsContent').html(`
                    <div class="alert alert-danger">
                        <h5><i class="icon fas fa-exclamation-triangle"></i> Error</h5>
                        ${errorMsg}
                        ${errorDetails}
                    </div>
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-primary" onclick="window.location.reload()">Refresh Page</button>
                    </div>
                `);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', xhr, status, error); // Debug log

            // Try to parse response if it's JSON
            var errorMessage = error;
            var errorDetails = '';

            try {
                if (xhr.responseText) {
                    var jsonResponse = JSON.parse(xhr.responseText);
                    if (jsonResponse.error) {
                        errorMessage = jsonResponse.error;
                    }
                    if (jsonResponse.details) {
                        errorDetails = jsonResponse.details;
                    }
                }
            } catch (e) {
                console.error('Error parsing JSON response:', e);
            }

            $('#bookingDetailsContent').html(`
                <div class="alert alert-danger">
                    <h5><i class="icon fas fa-exclamation-triangle"></i> Error</h5>
                    <p>An error occurred while fetching booking details: ${errorMessage}</p>
                    ${errorDetails ? '<div class="mt-2 small">Details: ' + errorDetails + '</div>' : ''}
                    <div class="mt-2 small">Status: ${status} (${xhr.status})</div>
                </div>
                <div class="text-center mt-3">
                    <button type="button" class="btn btn-primary" onclick="window.location.reload()">Refresh Page</button>
                    <button type="button" class="btn btn-secondary" onclick="window.viewBookingDetails(${bookingId})">Try Again</button>
                </div>
            `);
        }
    });
};

// FINAL EMERGENCY FIX: Function to update booking status using confirm-booking.php
function updateBookingStatus(bookingId, status, reason) {
    console.log('FINAL EMERGENCY FIX: updateBookingStatus called with ID:', bookingId, 'Status:', status);

    // Validate booking ID
    if (!bookingId || isNaN(parseInt(bookingId)) || parseInt(bookingId) <= 0) {
        alert('Invalid booking ID. Please try again or contact support.');
        console.error('Invalid booking ID detected for status update:', bookingId);
        return;
    }

    // Make sure any existing modals are hidden first
    $('.modal').modal('hide');

    // Show loading message
    $('#modalMessage').html('<div class="text-center"><i class="fas fa-spinner fa-spin"></i> Updating booking status...</div>');

    // Add a small delay before showing the new modal to ensure previous modals are fully closed
    setTimeout(function() {
        $('#actionModal').modal('show');
    }, 300);

    // Map status to action for confirm-booking.php
    var action = status === 'confirmed' ? 'confirm' : 'cancel';
    console.log('Mapped status to action:', status, '->', action);

    // Create simplified data object for confirm-booking.php
    var postData = {
        id: bookingId,
        action: action
    };

    console.log('Sending data:', postData);

    $.ajax({
        url: 'confirm-booking.php',
        type: 'POST',
        data: {
            id: bookingId,
            action: status === 'confirmed' ? 'confirm' : 'cancel'
        },
        dataType: 'json',
        success: function(response) {
            console.log('Response received:', response);
            if(response.success) {
                $('#modalMessage').html('<div class="alert alert-success"><i class="fas fa-check-circle"></i> Booking status updated successfully!</div>');

                // Set a timeout to reload the page
                setTimeout(function() {
                    window.location.reload();
                }, 1500);
            } else {
                $('#modalMessage').html('<div class="alert alert-danger"><i class="fas fa-exclamation-circle"></i> ' + (response.error || 'Error updating booking status') + '</div>');
                console.error('Error details:', response);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX error:', xhr.status, error);
            console.error('Response text:', xhr.responseText);

            // Try to parse the response if it's JSON
            var errorMessage = 'An error occurred while updating the booking status.';
            try {
                if (xhr.responseText) {
                    var jsonResponse = JSON.parse(xhr.responseText);
                    if (jsonResponse.error) {
                        errorMessage = jsonResponse.error;
                    }
                }
            } catch (e) {
                console.error('Error parsing JSON response:', e);
            }

            $('#modalMessage').html('<div class="alert alert-danger"><i class="fas fa-exclamation-circle"></i> ' + errorMessage + '</div>');
        }
    });
}

$(document).ready(function() {
    // Check if DataTable is already initialized
    if ($.fn.dataTable.isDataTable('#example1')) {
        // If already initialized, destroy it first
        $('#example1').DataTable().destroy();
    }

    // Initialize DataTable
    $("#example1").DataTable({
        "responsive": true,
        "lengthChange": true,
        "autoWidth": false,
        "buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"],
        "order": [[0, "desc"]],
        "pageLength": 10,
    }).buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');

    // FINAL EMERGENCY FIX: Accept booking with direct AJAX call
    $(document).on('click', '.accept-btn', function() {
        var id = $(this).data('id');
        console.log('Accept button clicked for booking ID:', id);
        if(confirm('Are you sure you want to accept this booking?')) {
            console.log('Direct confirm action for booking ID:', id);

            // Direct AJAX call to confirm-booking.php
            $.ajax({
                url: 'confirm-booking.php',
                type: 'POST',
                data: {
                    id: id,
                    action: 'confirm'
                },
                dataType: 'json',
                success: function(response) {
                    console.log('Response received:', response);
                    if(response.success) {
                        alert('Booking confirmed successfully!');
                        // Redirect to accepted bookings
                        window.location.href = 'accepted-bookings.php';
                    } else {
                        alert('Error: ' + (response.error || 'Unknown error'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', xhr.status, error);
                    console.error('Response text:', xhr.responseText);

                    // Try to parse the response if it's JSON
                    var errorMessage = 'Error confirming booking. Please try again.';
                    try {
                        if (xhr.responseText) {
                            var jsonResponse = JSON.parse(xhr.responseText);
                            if (jsonResponse.error) {
                                errorMessage = jsonResponse.error;
                                console.error('Parsed error:', jsonResponse.error);
                                if (jsonResponse.trace) {
                                    console.error('Error trace:', jsonResponse.trace);
                                }
                            }
                        }
                    } catch (e) {
                        console.error('Error parsing JSON response:', e);
                    }

                    alert(errorMessage);
                }
            });
        }
    });

    // FINAL EMERGENCY FIX: Reject booking with direct AJAX call
    $(document).on('click', '.reject-btn', function() {
        var id = $(this).data('id');
        console.log('Reject button clicked for booking ID:', id);
        if(confirm('Are you sure you want to reject this booking?')) {
            console.log('Direct cancel action for booking ID:', id);

            // Direct AJAX call to confirm-booking.php
            $.ajax({
                url: 'confirm-booking.php',
                type: 'POST',
                data: {
                    id: id,
                    action: 'cancel'
                },
                dataType: 'json',
                success: function(response) {
                    console.log('Response received:', response);
                    if(response.success) {
                        alert('Booking cancelled successfully!');
                        // Reload the page
                        window.location.reload();
                    } else {
                        console.error('Error details:', response);
                        alert('Error: ' + (response.error || 'Unknown error'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', xhr.status, error);
                    console.error('Response text:', xhr.responseText);

                    // Try to parse the response if it's JSON
                    var errorMessage = 'Error cancelling booking. Please try again.';
                    try {
                        if (xhr.responseText) {
                            var jsonResponse = JSON.parse(xhr.responseText);
                            if (jsonResponse.error) {
                                errorMessage = jsonResponse.error;
                                console.error('Parsed error:', jsonResponse.error);
                                if (jsonResponse.trace) {
                                    console.error('Error trace:', jsonResponse.trace);
                                }
                            }
                        }
                    } catch (e) {
                        console.error('Error parsing JSON response:', e);
                    }

                    alert(errorMessage);
                }
            });
        }
    });

    // View booking details
    $('.view-btn').click(function() {
        var id = $(this).data('id');

        // Validate booking ID
        if (!id || isNaN(parseInt(id)) || parseInt(id) <= 0) {
            alert('Invalid booking ID. Please try again or contact support.');
            console.error('Invalid booking ID detected:', id);
            return;
        }

        // Show loading indicator
        $('#bookingDetailsContent').html('<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">Loading booking details...</p></div>');
        $('#bookingDetailsModal').modal('show');

        $.ajax({
            url: 'get-booking-details.php',
            type: 'POST',
            data: { id: id },
            dataType: 'json',
            success: function(response) {
                console.log('Response received:', response); // Debug log

                if(response.success && response.data) {
                    var booking = response.data;

                    // Format currency function
                    function formatCurrency(amount) {
                        if (!amount) return '₱ 0.00';
                        return '₱ ' + parseFloat(amount).toLocaleString('en-US', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2
                        });
                    }

                    // Format date function
                    function formatDate(dateString) {
                        if (!dateString) return 'N/A';
                        try {
                            return new Date(dateString).toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                            });
                        } catch (e) {
                            console.error('Date formatting error:', e);
                            return dateString || 'N/A';
                        }
                    }

                    // Format time function
                    function formatTime(timeString) {
                        if (!timeString) return 'N/A';
                        try {
                            return new Date(timeString).toLocaleTimeString('en-US', {
                                hour: '2-digit',
                                minute: '2-digit'
                            });
                        } catch (e) {
                            console.error('Time formatting error:', e);
                            return timeString || 'N/A';
                        }
                    }

                    var details = `
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Booking Code</label>
                                <p class="form-control-plaintext">${booking.booking_code || 'N/A'}</p>
                            </div>
                            <div class="form-group">
                                <label>Customer Name</label>
                                <p class="form-control-plaintext">${booking.first_name || ''} ${booking.last_name || ''}</p>
                            </div>
                            <div class="form-group">
                                <label>Age</label>
                                <p class="form-control-plaintext">${booking.age || '25'}</p>
                            </div>
                            <div class="form-group">
                                <label>Sex</label>
                                <p class="form-control-plaintext">${booking.sex || 'Not Specified'}</p>
                            </div>
                            <div class="form-group">
                                <label>Email</label>
                                <p class="form-control-plaintext">${booking.email || 'N/A'}</p>
                            </div>
                            <div class="form-group">
                                <label>Contact Number</label>
                                <p class="form-control-plaintext">${booking.contact_number || 'N/A'}</p>
                            </div>
                            <div class="form-group">
                                <label>Address</label>
                                <p class="form-control-plaintext">${booking.address || 'N/A'}</p>
                            </div>
                            <div class="form-group">
                                <label>Emergency Contact Name</label>
                                <p class="form-control-plaintext">${booking.emergency_name || 'Emergency Contact'}</p>
                            </div>
                            <div class="form-group">
                                <label>Emergency Contact Number</label>
                                <p class="form-control-plaintext">${booking.emergency_number || '09123456789'}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Destination</label>
                                <p class="form-control-plaintext">${booking.destination_name || booking.tour_destination || 'Gigantes Island'}</p>
                            </div>
                            <div class="form-group">
                                <label>Drop-off Location</label>
                                <p class="form-control-plaintext">${booking.drop_off_location || 'Estancia'}</p>
                            </div>
                            <div class="form-group">
                                <label>Number of Pax</label>
                                <p class="form-control-plaintext">${booking.no_of_pax || '1'}</p>
                            </div>
                            <div class="form-group">
                                <label>Boat</label>
                                <p class="form-control-plaintext">${booking.boat_name || 'Assigned by Tourism Office'} <span class="text-muted small">(The Tourism Office will arrange the boat for you)</span></p>
                            </div>
                            <div class="form-group">
                                <label>Start Date</label>
                                <p class="form-control-plaintext">${formatDate(booking.start_date)}</p>
                            </div>
                            <div class="form-group">
                                <label>End Date</label>
                                <p class="form-control-plaintext">${formatDate(booking.end_date)}</p>
                            </div>
                            <div class="form-group">
                                <label>Booking Time</label>
                                <p class="form-control-plaintext">${formatTime(booking.booking_time)}</p>
                            </div>
                            <div class="form-group">
                                <label>Environmental Fee</label>
                                <p class="form-control-plaintext">${formatCurrency(booking.environmental_fee || 75)}</p>
                            </div>
                            <div class="form-group">
                                <label>Payment Method</label>
                                <p class="form-control-plaintext">${booking.payment_method || 'Cash'}</p>
                            </div>
                            <div class="form-group">
                                <label>Total Amount</label>
                                <p class="form-control-plaintext">${formatCurrency(booking.total)}</p>
                            </div>
                            <div class="form-group">
                                <label>Status</label>
                                <p class="form-control-plaintext">
                                    <span class="badge bg-warning">Pending</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12 text-center">
                            <button class="btn btn-success accept-booking-btn" data-id="${booking.booking_id}">
                                <i class="fas fa-check"></i> Accept Booking
                            </button>
                            <button class="btn btn-danger ml-2 reject-booking-btn" data-id="${booking.booking_id}">
                                <i class="fas fa-times"></i> Reject Booking
                            </button>
                        </div>
                    </div>
                    `;
                    $('#bookingDetailsContent').html(details);

                    // FINAL EMERGENCY FIX: Add event listeners for the accept/reject buttons in the modal
                    $('.accept-booking-btn').click(function() {
                        var bookingId = $(this).data('id');
                        if(confirm('Are you sure you want to accept this booking?')) {
                            console.log('Modal: Direct confirm action for booking ID:', bookingId);

                            // Direct AJAX call to confirm-booking.php
                            $.ajax({
                                url: 'confirm-booking.php',
                                type: 'POST',
                                data: {
                                    id: bookingId,
                                    action: 'confirm'
                                },
                                dataType: 'json',
                                success: function(response) {
                                    console.log('Response received:', response);
                                    if(response.success) {
                                        alert('Booking confirmed successfully!');
                                        // Redirect to accepted bookings
                                        window.location.href = 'accepted-bookings.php';
                                    } else {
                                        alert('Error: ' + (response.error || 'Unknown error'));
                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error('AJAX error:', xhr.status, error);
                                    console.error('Response text:', xhr.responseText);

                                    // Try to parse the response if it's JSON
                                    var errorMessage = 'Error confirming booking. Please try again.';
                                    try {
                                        if (xhr.responseText) {
                                            var jsonResponse = JSON.parse(xhr.responseText);
                                            if (jsonResponse.error) {
                                                errorMessage = jsonResponse.error;
                                                console.error('Parsed error:', jsonResponse.error);
                                                if (jsonResponse.trace) {
                                                    console.error('Error trace:', jsonResponse.trace);
                                                }
                                            }
                                        }
                                    } catch (e) {
                                        console.error('Error parsing JSON response:', e);
                                    }

                                    alert(errorMessage);
                                }
                            });
                        }
                    });

                    $('.reject-booking-btn').click(function() {
                        var bookingId = $(this).data('id');
                        if(confirm('Are you sure you want to reject this booking?')) {
                            console.log('Modal: Direct cancel action for booking ID:', bookingId);

                            // Direct AJAX call to confirm-booking.php
                            $.ajax({
                                url: 'confirm-booking.php',
                                type: 'POST',
                                data: {
                                    id: bookingId,
                                    action: 'cancel'
                                },
                                dataType: 'json',
                                success: function(response) {
                                    console.log('Response received:', response);
                                    if(response.success) {
                                        alert('Booking cancelled successfully!');
                                        // Reload the page
                                        window.location.reload();
                                    } else {
                                        console.error('Error details:', response);
                                        alert('Error: ' + (response.error || 'Unknown error'));
                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error('AJAX error:', xhr.status, error);
                                    console.error('Response text:', xhr.responseText);

                                    // Try to parse the response if it's JSON
                                    var errorMessage = 'Error cancelling booking. Please try again.';
                                    try {
                                        if (xhr.responseText) {
                                            var jsonResponse = JSON.parse(xhr.responseText);
                                            if (jsonResponse.error) {
                                                errorMessage = jsonResponse.error;
                                                console.error('Parsed error:', jsonResponse.error);
                                                if (jsonResponse.trace) {
                                                    console.error('Error trace:', jsonResponse.trace);
                                                }
                                            }
                                        }
                                    } catch (e) {
                                        console.error('Error parsing JSON response:', e);
                                    }

                                    alert(errorMessage);
                                }
                            });
                        }
                    });

                } else {
                    // Show error with details if available
                    var errorMsg = response.error || 'Failed to load booking details';
                    var errorDetails = '';

                    if (response.details) {
                        errorDetails = `<div class="mt-2 small text-muted">Details: ${response.details}</div>`;
                    }

                    $('#bookingDetailsContent').html(`
                        <div class="alert alert-danger">
                            <h5><i class="icon fas fa-exclamation-triangle"></i> Error</h5>
                            ${errorMsg}
                            ${errorDetails}
                        </div>
                        <div class="text-center mt-3">
                            <button type="button" class="btn btn-primary" onclick="window.location.reload()">Refresh Page</button>
                        </div>
                    `);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr, status, error); // Debug log

                // Try to parse response if it's JSON
                var errorMessage = error;
                var errorDetails = '';

                try {
                    if (xhr.responseText) {
                        var jsonResponse = JSON.parse(xhr.responseText);
                        if (jsonResponse.error) {
                            errorMessage = jsonResponse.error;
                        }
                        if (jsonResponse.details) {
                            errorDetails = jsonResponse.details;
                        }
                    }
                } catch (e) {
                    console.error('Error parsing JSON response:', e);
                }

                $('#bookingDetailsContent').html(`
                    <div class="alert alert-danger">
                        <h5><i class="icon fas fa-exclamation-triangle"></i> Error</h5>
                        <p>An error occurred while fetching booking details: ${errorMessage}</p>
                        ${errorDetails ? '<div class="mt-2 small">Details: ' + errorDetails + '</div>' : ''}
                        <div class="mt-2 small">Status: ${status} (${xhr.status})</div>
                    </div>
                    <div class="text-center mt-3">
                        <button type="button" class="btn btn-primary" onclick="window.location.reload()">Refresh Page</button>
                        <button type="button" class="btn btn-secondary" onclick="window.viewBookingDetails(${id})">Try Again</button>
                    </div>
                `);
            }
        });
    });

    // Edit booking
    $(document).on('click', '.edit-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();

        var id = $(this).data('id');
        console.log('Edit button clicked for booking ID:', id);

        // Make sure any existing modals are hidden first
        $('.modal').modal('hide');
        $('.modal-backdrop').remove();

        // Load boats and destinations first
        loadBoatsAndDestinations();

        // Then load booking details
        $.ajax({
            url: 'get-booking-details.php',
            type: 'POST',
            data: { id: id },
            dataType: 'json',
            success: function(response) {
                if(response.success && response.data) {
                    var booking = response.data;
                    console.log('Booking data loaded:', booking);

                    try {
                        // Format dates for input fields with error handling
                        var startDate = '';
                        var endDate = '';
                        var bookingTime = '';

                        try {
                            if (booking.start_date) {
                                startDate = new Date(booking.start_date).toISOString().split('T')[0];
                            }
                        } catch (e) {
                            console.error('Error formatting start date:', e);
                            startDate = '';
                        }

                        try {
                            if (booking.end_date) {
                                endDate = new Date(booking.end_date).toISOString().split('T')[0];
                            }
                        } catch (e) {
                            console.error('Error formatting end date:', e);
                            endDate = '';
                        }

                        try {
                            if (booking.booking_time) {
                                bookingTime = new Date(booking.booking_time).toTimeString().slice(0,5);
                            }
                        } catch (e) {
                            console.error('Error formatting booking time:', e);
                            bookingTime = '';
                        }

                        // Set form values
                        $('#editBookingId').val(booking.booking_id);
                        $('#editBookingCode').val(booking.booking_code);

                        // Set customer name in both visible field and hidden field
                        var customerName = (booking.first_name || '') + ' ' + (booking.last_name || '');
                        $('#editCustomerName').val(customerName);
                        $('#editCustomerNameHidden').val(customerName);

                        $('#editEmail').val(booking.email || '');
                        $('#editContactNumber').val(booking.contact_number || '');
                        $('#editBoat').val(booking.boat_id || '');
                        $('#editDestination').val(booking.destination_id || '');
                        $('#editStartDate').val(startDate);
                        $('#editEndDate').val(endDate);
                        $('#editBookingTime').val(bookingTime);
                        $('#editTotal').val(booking.total || 0);

                        // Add a small delay before showing the modal
                        setTimeout(function() {
                            $('#editBookingModal').modal({
                                backdrop: 'static',
                                keyboard: false
                            });
                        }, 300);
                    } catch (error) {
                        console.error('Error setting form values:', error);
                        alert('Error setting form values: ' + error.message);
                    }
                } else {
                    alert('Error loading booking details: ' + (response.error || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', xhr.status, error);
                console.error('Response text:', xhr.responseText);
                alert('Error loading booking details. Please try again.');
            }
        });
    });

    // Load boats and destinations
    function loadBoatsAndDestinations() {
        // Load boats
        $.ajax({
            url: 'get-options.php',
            type: 'GET',
            data: { type: 'boats' },
            success: function(response) {
                $('#editBoat').html(response);
            },
            error: function() {
                alert('Error loading boats');
            }
        });

        // Load destinations
        $.ajax({
            url: 'get-options.php',
            type: 'GET',
            data: { type: 'destinations' },
            success: function(response) {
                $('#editDestination').html(response);
            },
            error: function() {
                alert('Error loading destinations');
            }
        });
    }

    // Save edited booking
    $(document).on('click', '#saveEdit', function(e) {
        e.preventDefault();
        e.stopPropagation();

        console.log('Save Edit button clicked');

        var formData = $('#editBookingForm').serialize();
        console.log('Form data:', formData);

        // Validate dates
        var startDateStr = $('#editStartDate').val();
        var endDateStr = $('#editEndDate').val();

        if (!startDateStr || !endDateStr) {
            alert('Please select both start and end dates');
            return;
        }

        var startDate = new Date(startDateStr);
        var endDate = new Date(endDateStr);

        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            alert('Invalid date format. Please use YYYY-MM-DD format.');
            return;
        }

        if (endDate < startDate) {
            alert('End date cannot be before start date');
            return;
        }

        // Show loading message
        $('#saveEdit').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Saving...');

        $.ajax({
            url: 'update-booking.php',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                console.log('Update response:', response);

                if(response.success) {
                    // Close modal and remove backdrop
                    $('#editBookingModal').modal('hide');
                    $('.modal-backdrop').remove();

                    // Show success message
                    alert('Booking updated successfully');

                    // Reload page after a short delay
                    setTimeout(function() {
                        window.location.reload();
                    }, 500);
                } else {
                    // Re-enable save button
                    $('#saveEdit').prop('disabled', false).text('Save Changes');

                    // Show error message
                    alert(response.error || 'Error updating booking');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', xhr.status, error);
                console.error('Response text:', xhr.responseText);

                // Re-enable save button
                $('#saveEdit').prop('disabled', false).text('Save Changes');

                // Try to parse the response if it's JSON
                var errorMessage = 'Error updating booking. Please try again.';
                try {
                    if (xhr.responseText) {
                        var jsonResponse = JSON.parse(xhr.responseText);
                        if (jsonResponse.error) {
                            errorMessage = jsonResponse.error;
                            console.error('Parsed error:', jsonResponse.error);
                        }
                    }
                } catch (e) {
                    console.error('Error parsing JSON response:', e);
                }

                alert(errorMessage);
            }
        });
    });
});
</script>
</body>
</html>
