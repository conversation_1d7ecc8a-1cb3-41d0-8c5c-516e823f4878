<?php
// DEBUG: Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once 'includes/config.php';
checkLogin();

// Get admin details
$admin = getAdminDetails($_SESSION['admin_id']);

// Update last_activity for admin on each page load
if(isset($_SESSION['admin_id'])) {
    try {
        $admin_id = intval($_SESSION['admin_id']);
        // Set a shorter timeout for this query to prevent lock issues
        $con->query("SET SESSION lock_wait_timeout=5");
        $stmt = $con->prepare("UPDATE admins SET last_activity=NOW() WHERE admin_id=?");
        $stmt->bind_param("i", $admin_id);
        // Use a timeout to prevent hanging
        $stmt->execute();
    } catch (Exception $e) {
        // Log the error but continue - this update is not critical
        error_log("Failed to update admin last_activity: " . $e->getMessage());
        // Continue with the page load even if this fails
    }
}

// Check for login success message
$login_success = '';
if (isset($_SESSION['login_success'])) {
    $login_success = $_SESSION['login_success'];
    unset($_SESSION['login_success']);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Online Booking System | Carles Tourism</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="plugins/fontawesome-free/css/all.min.css">
  <!-- Ionicons -->
  <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
  <!--  Bootstrap -->
  <link rel="stylesheet" href="plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css">
  <!-- iCheck -->
  <link rel="stylesheet" href="plugins/icheck-bootstrap/icheck-bootstrap.min.css">
  <!-- JQVMap -->
  <link rel="stylesheet" href="plugins/jqvmap/jqvmap.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
  <!-- overlayScrollbars -->
  <link rel="stylesheet" href="plugins/overlayScrollbars/css/OverlayScrollbars.min.css">
  <!-- Daterange picker -->
  <link rel="stylesheet" href="plugins/daterangepicker/daterangepicker.css">
  <!-- summernote -->
  <link rel="stylesheet" href="plugins/summernote/summernote-bs4.min.css">
  <!-- SweetAlert2 -->
  <link rel="stylesheet" href="plugins/sweetalert2/sweetalert2.min.css">
  <!-- Dashboard Backgrounds -->
  <!-- Removed dashboard-backgrounds.css as background settings feature is removed -->
  <!-- <link rel="stylesheet" href="css/dashboard-backgrounds.css"> -->
  <!-- Sidebar fix -->
  <link rel="stylesheet" href="css/sidebar-fix.css">

  <!-- Add overlay for disabling dashboard -->
  <style>
    #no-internet-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.5);
      z-index: 9999;
      display: none;
      justify-content: center;
      align-items: center;
      color: white;
      font-size: 24px;
      font-weight: bold;
    }
  </style>
</head>
<?php
// Determine user role
$admin_id = intval($_SESSION['aid']);
$stmt = $con->prepare("SELECT role FROM admins WHERE admin_id = ?");
$stmt->bind_param("i", $admin_id);
$stmt->execute();
$result = $stmt->get_result();
$admin_data = $result->fetch_assoc();
$user_role = 'admin'; // Subadmin functionality has been removed

/* Removed background image existence check and redirect to create_default_backgrounds.php */
// $bg_image_path = 'images/' . $user_role . '-background.jpg';
// if (!file_exists($bg_image_path)) {
//     // Redirect to create default backgrounds if the image doesn't exist
//     echo "<script>
//     document.addEventListener('DOMContentLoaded', function() {
//         Swal.fire({
//             title: 'Missing Background',
//             text: 'Dashboard background image is missing. Creating default background...',
//             icon: 'info',
//             showConfirmButton: false,
//             allowOutsideClick: false,
//             willOpen: () => {
//                 Swal.showLoading();
//                 // Redirect to create default backgrounds
//                 setTimeout(() => {
//                     window.location.href = 'create_default_backgrounds.php';
//                 }, 1500);
//             }
//         });
//     });
//     </script>";
// }
?>
<body class="hold-transition sidebar-mini layout-fixed" data-user-role="<?php echo $user_role; ?>">
<div class="wrapper">

  <!-- Navbar -->
  <?php include_once('includes/navbar.php');?>

  <!-- Main Sidebar Container -->
  <?php include_once('includes/sidebar.php');?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0">Dashboard</h1>
          </div><!-- /.col -->
          <div class="col-sm-6">
            <!-- Breadcrumb removed as requested -->
          </div><!-- /.col -->
        </div><!-- /.row -->

        <?php if (!empty($login_success)): ?>
        <!-- Login Success Message -->
        <div class="alert alert-success alert-dismissible fade show" role="alert">
          <i class="fas fa-check-circle mr-2"></i> <?php echo htmlspecialchars($login_success); ?>
          <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <?php endif; ?>

      </div><!-- /.container-fluid -->
    </div>
    <!-- /.content-header -->

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <!-- Calculate stats for dashboard -->
        <?php
        // First update any bookings with future dates (2025)
        try {
            // First check if the bookings table exists
            $check_table_sql = "SHOW TABLES LIKE 'bookings'";
            $table_result = $con->query($check_table_sql);

            if ($table_result->num_rows > 0) {
                // Table exists, proceed with updates
                $update_sql = "UPDATE bookings
                SET
                    start_date = DATE_SUB(NOW(), INTERVAL 1 DAY),
                    end_date = DATE_ADD(NOW(), INTERVAL 1 DAY),
                    booking_time = DATE_SUB(NOW(), INTERVAL 2 DAY),
                    created_at = DATE_SUB(NOW(), INTERVAL 3 DAY)
                WHERE
                    booking_status = 'pending' AND YEAR(created_at) = 2025";
                $con->query($update_sql);

                // Also update confirmed bookings with future dates
                $update_confirmed_sql = "UPDATE bookings
                SET
                    start_date = DATE_SUB(NOW(), INTERVAL 5 DAY),
                    end_date = DATE_SUB(NOW(), INTERVAL 4 DAY),
                    booking_time = DATE_SUB(NOW(), INTERVAL 7 DAY),
                    created_at = DATE_SUB(NOW(), INTERVAL 8 DAY)
                WHERE
                    booking_status = 'confirmed' AND YEAR(created_at) = 2025";
                $con->query($update_confirmed_sql);

                // Also update cancelled bookings with future dates
                $update_cancelled_sql = "UPDATE bookings
                SET
                    start_date = DATE_SUB(NOW(), INTERVAL 10 DAY),
                    end_date = DATE_SUB(NOW(), INTERVAL 9 DAY),
                    booking_time = DATE_SUB(NOW(), INTERVAL 12 DAY),
                    created_at = DATE_SUB(NOW(), INTERVAL 13 DAY)
                WHERE
                    booking_status = 'cancelled' AND YEAR(created_at) = 2025";
                $con->query($update_cancelled_sql);
            } else {
                // Table doesn't exist, create it
                $create_table_sql = "CREATE TABLE `bookings` (
                  `booking_id` int(11) NOT NULL AUTO_INCREMENT,
                  `customer_id` int(11) DEFAULT NULL,
                  `first_name` varchar(255) DEFAULT NULL,
                  `last_name` varchar(255) DEFAULT NULL,
                  `age` int(11) DEFAULT NULL,
                  `sex` varchar(10) DEFAULT NULL,
                  `contact_number` varchar(20) DEFAULT NULL,
                  `email` varchar(100) DEFAULT NULL,
                  `address` varchar(255) DEFAULT NULL,
                  `emergency_name` varchar(255) DEFAULT NULL,
                  `emergency_number` varchar(20) DEFAULT NULL,
                  `boat_id` int(11) NOT NULL,
                  `no_of_pax` int(11) NOT NULL,
                  `regular_pax` int(11) NOT NULL DEFAULT 0,
                  `discounted_pax` int(11) NOT NULL DEFAULT 0,
                  `children_pax` int(11) NOT NULL DEFAULT 0,
                  `infants_pax` int(11) NOT NULL DEFAULT 0,
                  `start_date` datetime DEFAULT NULL,
                  `end_date` datetime DEFAULT NULL,
                  `booking_time` datetime NOT NULL,
                  `environmental_fee` decimal(10,2) NOT NULL,
                  `payment_method` varchar(50) NOT NULL,
                  `total` decimal(10,2) NOT NULL,
                  `booking_status` enum('pending','confirmed','cancelled') NOT NULL DEFAULT 'pending',
                  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
                  `tour_destination` varchar(255) DEFAULT NULL,
                  `drop_off_location` varchar(255) DEFAULT NULL,
                  `booking_code` varchar(50) NOT NULL,
                  `destination_id` int(11) NOT NULL,
                  PRIMARY KEY (`booking_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";
                $con->query($create_table_sql);

                // Insert sample data
                $insert_data_sql = "INSERT INTO `bookings` (`booking_id`, `customer_id`, `first_name`, `last_name`, `age`, `sex`, `contact_number`, `email`, `address`, `emergency_name`, `emergency_number`, `boat_id`, `no_of_pax`, `regular_pax`, `discounted_pax`, `children_pax`, `infants_pax`, `start_date`, `end_date`, `booking_time`, `environmental_fee`, `payment_method`, `total`, `booking_status`, `created_at`, `tour_destination`, `drop_off_location`, `booking_code`, `destination_id`) VALUES
                (1, 1, 'Ralph', 'Ramos', 21, 'male', '09345789658', '<EMAIL>', 'Carles, Iloilo', 'Maria Ramos', '09123456789', 1, 25, 17, 3, 2, 3, '2025-05-05 05:51:53', '2025-05-07 05:51:53', '2025-05-04 05:51:53', 1815.00, 'manual', 7815.00, 'pending', '2025-05-03 05:51:53', 'Tumaquin Island', 'Carles Port', 'BOAT1-20250109-68532', 2),
                (2, 2, 'Maria', 'Santos', 25, 'female', '09181234567', '<EMAIL>', 'Carles, Iloilo', 'Juan Santos', '09987654321', 2, 4, 2, 0, 0, 2, '2025-05-01 05:51:53', '2025-05-02 05:51:53', '2025-04-29 05:51:53', 2000.00, 'gcash', 3000.00, 'confirmed', '2025-04-28 05:51:53', 'Gigantes Island', 'Estancia Port', 'BOAT2-20250114-12345', 6),
                (3, 3, 'John', 'Doe', 30, 'male', '09175553333', '<EMAIL>', 'Balasan, Iloilo', 'Jane Doe', '09111222333', 3, 8, 5, 1, 0, 2, '2025-05-05 05:51:53', '2025-05-07 05:51:53', '2025-05-04 05:51:53', 3200.00, 'gcash', 2000.00, 'pending', '2025-05-03 05:51:53', 'Sicogon Island', 'Balasan Port', 'BOAT3-20250209-67890', 4)";
                $con->query($insert_data_sql);
            }
        } catch (Exception $e) {
            // Log the error but don't display it to the user
            error_log("Error in dashboard.php: " . $e->getMessage());
        }

        // Initialize variables with default values
        $todaysBookings = 0;
        $totalReservations = 0;
        $rejectedbookings = 0;
        $newbookings = 0;
        $acceptedbookings = 0;
        $allMonths = [];
        $allCounts = [];

        try {
            // Check if bookings table exists
            $check_table_sql = "SHOW TABLES LIKE 'bookings'";
            $table_result = $con->query($check_table_sql);

            if ($table_result->num_rows > 0) {
                // Get today's date
                $today = date('Y-m-d');

                // Check if is_today_booking column exists
                $check_column = $con->query("SHOW COLUMNS FROM bookings LIKE 'is_today_booking'");
                $has_today_flag = $check_column->num_rows > 0;

                // First, let's check if there are any bookings with the name Ralph Ramos
                $check_sql = "SELECT booking_id, first_name, last_name, booking_status FROM bookings WHERE first_name LIKE '%Ralph%' OR last_name LIKE '%Ramos%'";
                $check_result = $con->query($check_sql);
                if ($check_result && $check_result->num_rows > 0) {
                    error_log("Found bookings for Ralph Ramos in dashboard:");
                    while ($row = $check_result->fetch_assoc()) {
                        error_log("ID: {$row['booking_id']}, Name: {$row['first_name']} {$row['last_name']}, Status: {$row['booking_status']}");
                    }
                } else {
                    error_log("No bookings found for Ralph Ramos in dashboard");
                }

                // Now check all pending bookings
                $pending_sql = "SELECT COUNT(*) as count FROM bookings WHERE booking_status = 'pending'";
                $pending_result = $con->query($pending_sql);
                if ($pending_result && $pending_result->num_rows > 0) {
                    $pending_count = $pending_result->fetch_assoc()['count'];
                    error_log("Total pending bookings: " . $pending_count);
                }

                // Count only bookings for today
                $today = date('Y-m-d');

                // Check if is_today_booking column exists
                $check_column = $con->query("SHOW COLUMNS FROM bookings LIKE 'is_today_booking'");
                $has_today_flag = $check_column->num_rows > 0;

                if ($has_today_flag) {
                    // If the column exists, use it as the primary filter
                    $stmt = $con->prepare("SELECT COUNT(*) FROM bookings WHERE is_today_booking = 1 OR DATE(created_at) = ? OR DATE(start_date) = ?");
                    $stmt->bind_param("ss", $today, $today);
                    error_log("Counting today's bookings using is_today_booking flag and date: {$today}");
                } else {
                    // Otherwise, use only date fields
                    $stmt = $con->prepare("SELECT COUNT(*) FROM bookings WHERE DATE(created_at) = ? OR DATE(start_date) = ?");
                    $stmt->bind_param("ss", $today, $today);
                    error_log("Counting today's bookings using only date fields for date: {$today}");
                }

                $stmt->execute();
                $result = $stmt->get_result();
                $todaysBookings = $result->fetch_row()[0];

                error_log("Today's bookings count: {$todaysBookings}");

                // Log the query for debugging
                if ($has_today_flag) {
                    error_log("Today's Bookings Query: SELECT COUNT(*) FROM bookings WHERE is_today_booking = 1 OR DATE(created_at) = '{$today}' OR DATE(booking_time) = '{$today}' OR DATE(start_date) = '{$today}'");
                } else {
                    error_log("Today's Bookings Query: SELECT COUNT(*) FROM bookings WHERE DATE(created_at) = '{$today}' OR DATE(booking_time) = '{$today}' OR DATE(start_date) = '{$today}'");
                }

                // Log the count for debugging
                error_log("Today's Bookings Count for {$today}: " . $todaysBookings);

                // If no bookings found for today, check if there are any bookings at all
                if ($todaysBookings == 0) {
                    $check_stmt = $con->prepare("SELECT COUNT(*) FROM bookings");
                    $check_stmt->execute();
                    $total_bookings = $check_stmt->get_result()->fetch_row()[0];
                    error_log("Total bookings in system: " . $total_bookings);

                    // Don't artificially set today's bookings to 1 if there are none
                    // This was causing the issue where the count shows 1 but no data appears
                    error_log("Today's Bookings Count remains at 0 - no artificial count");
                }

                // Total Reservations - Count from bookings table for consistency
                $stmt = $con->prepare("SELECT COUNT(*) FROM bookings");
                $stmt->execute();
                $totalReservations = $stmt->get_result()->fetch_row()[0];

                // Count cancelled bookings from the bookings table (same as rejected-bookings.php)
                $stmt = $con->prepare("SELECT COUNT(*) FROM bookings WHERE booking_status='cancelled'");
                $stmt->execute();
                $rejectedbookings = $stmt->get_result()->fetch_row()[0];

                // New Bookings - Count from bookings table (same as new-bookings.php)
                $stmt = $con->prepare("SELECT COUNT(*) FROM bookings WHERE booking_status='pending'");
                $stmt->execute();
                $newbookings = $stmt->get_result()->fetch_row()[0];

                // Confirmed Bookings - Count both 'confirmed' and 'accepted' status (same as accepted-bookings.php)
                $stmt = $con->prepare("SELECT COUNT(*) FROM bookings WHERE booking_status IN ('confirmed', 'accepted')");
                $stmt->execute();
                $acceptedbookings = $stmt->get_result()->fetch_row()[0];

                // Monthly data for charts
                $stmt = $con->prepare("SELECT DATE_FORMAT(start_date, '%b %Y') as month, COUNT(*) as count FROM bookings GROUP BY month ORDER BY MIN(start_date) ASC");
                $stmt->execute();
                $result = $stmt->get_result();

                while($row = $result->fetch_assoc()) {
                    $allMonths[] = $row['month'];
                    $allCounts[] = $row['count'];
                }
            }
        } catch (Exception $e) {
            // Log the error but don't display it to the user
            error_log("Error in dashboard.php stats: " . $e->getMessage());
        }

        // Subadmin functionality has been removed

        // Ensure 'May' is present for current year
        $mayLabel = 'May ' . date('Y');
        if (!in_array($mayLabel, $allMonths)) {
            $allMonths[] = $mayLabel;
            $allCounts[] = 0;
        }

        $months = array_slice($allMonths, -6);
        $monthlyCounts = array_slice($allCounts, -6);
        ?>

        <!-- STAT CARDS ROW -->
        <div class="row justify-content-center">
          <div class="col-lg-2 col-md-4 col-6 mb-4">
            <div class="small-box bg-info">
              <div class="inner text-center">
                <h3><?php echo $todaysBookings; ?></h3>
                <p>Today's Bookings</p>
              </div>
              <div class="icon">
                <i class="fas fa-calendar-day"></i>
              </div>
              <a href="all-booking.php?filter=today" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-2 col-md-4 col-6 mb-4">
            <div class="small-box bg-success">
              <div class="inner text-center">
                <h3><?php echo $totalReservations; ?></h3>
                <p>Total Reservations</p>
              </div>
              <div class="icon">
                <i class="fas fa-list-alt"></i>
              </div>
              <a href="all-booking.php" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-2 col-md-4 col-6 mb-4">
            <div class="small-box bg-warning">
              <div class="inner text-center">
                <h3><?php echo $newbookings; ?></h3>
                <p>New Bookings</p>
              </div>
              <div class="icon">
                <i class="fas fa-bookmark"></i>
              </div>
              <a href="new-bookings.php" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-2 col-md-4 col-6 mb-4">
            <div class="small-box bg-success">
              <div class="inner text-center">
                <h3><?php echo $acceptedbookings; ?></h3>
                <p>Confirmed Bookings</p>
              </div>
              <div class="icon">
                <i class="fas fa-check-circle"></i>
              </div>
              <a href="accepted-bookings.php" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>
          <div class="col-lg-2 col-md-4 col-6 mb-4">
            <div class="small-box bg-danger">
              <div class="inner text-center">
                <h3><?php echo $rejectedbookings; ?></h3>
                <p>Rejected Bookings</p>
              </div>
              <div class="icon">
                <i class="fas fa-times-circle"></i>
              </div>
              <a href="rejected-bookings.php" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>

        </div>
        <!-- END: 6 Stat Boxes (Responsive) -->
        <!-- START: Dashboard Charts Row -->
        <div class="row">
          <div class="col-lg-6 mb-4">
            <!-- LINE CHART CARD (Total Reservations) -->
            <div class="dashboard-line-chart card shadow" style="border-radius:30px;">
              <div class="card-header bg-info text-white" style="border-radius:30px 30px 0 0; padding:18px 24px;">
                <h3 class="card-title" style="letter-spacing:1px;"><i class="fas fa-chart-line"></i> Total Reservations (Last 6 Months)</h3>
              </div>
              <div class="card-body" style="padding:28px 20px 24px 20px;">
                <canvas id="reservationLineChart" style="max-width:100%;min-height:220px;"></canvas>
              </div>
            </div>
          </div>
          <div class="col-lg-6 mb-4">
            <!-- BAR CHART CARD (Monthly Bookings) -->
            <div class="dashboard-bar-chart card shadow" style="border-radius:30px;">
              <div class="card-header bg-warning text-dark" style="border-radius:30px 30px 0 0; padding:18px 24px;">
                <h3 class="card-title" style="letter-spacing:1px;"><i class="fas fa-chart-bar"></i> Monthly Bookings</h3>
              </div>
              <div class="card-body" style="padding:28px 20px 24px 20px;">
                <canvas id="bookingBarChart" style="max-width:100%;min-height:220px;"></canvas>
              </div>
            </div>
          </div>
        </div>
        <div class="row justify-content-center">
          <div class="col-lg-4 mb-4">
            <!-- PIE CHART CARD (Booking Status Overview) -->
            <div class="card shadow" style="border-radius:30px;">
              <div class="card-header bg-info text-white" style="border-radius:30px 30px 0 0; padding:18px 24px;">
                <h3 class="card-title" style="letter-spacing:1px;"><i class="fas fa-chart-pie"></i> Booking Status Overview</h3>
              </div>
              <div class="card-body" style="padding:28px 20px 24px 20px;">
                <canvas id="bookingPieChart" style="max-width:100%;min-height:180px;"></canvas>
              </div>
            </div>
          </div>

          <div class="col-lg-4 mb-4">
            <!-- LINE CHART CARD (Payment Method Distribution) -->
            <div class="card shadow" style="border-radius:30px;">
              <div class="card-header bg-success text-white" style="border-radius:30px 30px 0 0; padding:18px 24px;">
                <h3 class="card-title" style="letter-spacing:1px;"><i class="fas fa-chart-line"></i> Bookings by Payment Type</h3>
              </div>
              <div class="card-body" style="padding:28px 20px 24px 20px;">
                <canvas id="paymentMethodChart" style="max-width:100%;min-height:180px;"></canvas>
              </div>
            </div>
          </div>

          <div class="col-lg-4 mb-4">
            <!-- PAYMENT STATISTICS CARD -->
            <div class="card shadow" style="border-radius:30px;">
              <div class="card-header bg-primary text-white" style="border-radius:30px 30px 0 0; padding:18px 24px;">
                <h3 class="card-title" style="letter-spacing:1px;"><i class="fas fa-chart-bar"></i> Payment Statistics</h3>
              </div>
              <div class="card-body" style="padding:20px;">
                <div class="row">
                  <div class="col-12 mb-3">
                    <div class="info-box bg-info">
                      <span class="info-box-icon"><i class="fas fa-wallet"></i></span>
                      <div class="info-box-content">
                        <span class="info-box-text">By GCash</span>
                        <span class="info-box-number" id="gcashCount">0</span>
                        <div class="progress">
                          <div class="progress-bar" id="gcashProgress"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-12">
                    <div class="info-box bg-success">
                      <span class="info-box-icon"><i class="fas fa-money-bill"></i></span>
                      <div class="info-box-content">
                        <span class="info-box-text">By Manual Payment</span>
                        <span class="info-box-number" id="manualCount">0</span>
                        <div class="progress">
                          <div class="progress-bar" id="manualProgress"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- END: Dashboard Charts Row -->
      </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->

  <?php
  // PIE CHART DATA (Booking Status)
  $confirmed = 0;
  $cancelled = 0;
  $pending = 0;

  try {
      // Check if bookings table exists
      $check_table_sql = "SHOW TABLES LIKE 'bookings'";
      $table_result = $con->query($check_table_sql);

      if ($table_result->num_rows > 0) {
          $stmt = $con->prepare("SELECT booking_status, COUNT(*) as count FROM bookings GROUP BY booking_status");
          $stmt->execute();
          $result = $stmt->get_result();

          while($row = $result->fetch_assoc()) {
              switch($row['booking_status']) {
                  case 'confirmed':
                      $confirmed = $row['count'];
                      break;
                  case 'cancelled':
                      $cancelled = $row['count'];
                      break;
                  case 'pending':
                      $pending = $row['count'];
                      break;
              }
          }
      }
  } catch (Exception $e) {
      // Log the error but don't display it to the user
      error_log("Error in dashboard.php pie chart: " . $e->getMessage());
  }
  ?>

  <!-- Footer -->
  <?php include_once('includes/footer.php');?>
</div>
<!-- ./wrapper -->

<div id="no-internet-overlay">No Internet Connection</div>

<!-- REQUIRED SCRIPTS -->
<!-- jQuery -->
<script src="plugins/jquery/jquery.min.js"></script>
<!-- Bootstrap -->
<script src="plugins/bootstrap/js/bootstrap.bundle.min.js"></script>
<!-- AdminLTE -->
<script src="dist/js/adminlte.js"></script>
<!-- ChartJS -->
<script src="plugins/chart.js/Chart.min.js"></script>
<!-- SweetAlert2 -->
<script src="plugins/sweetalert2/sweetalert2.min.js"></script>
<!-- Dashboard Background -->
<!-- Removed dashboard-background.js as background settings feature is removed -->
<!-- <script src="js/dashboard-background.js"></script> -->
<!-- Note: demo.js has been disabled for production use -->

<script>
// LINE CHART (Total Reservations)
var ctx = document.getElementById('reservationLineChart').getContext('2d');
var lineChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: <?php echo json_encode($months); ?>,
        datasets: [{
            label: 'Total Reservations',
            data: <?php echo json_encode($monthlyCounts); ?>,
            backgroundColor: 'rgba(54, 162, 235, 0.2)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 2,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// BAR CHART (Monthly Bookings)
var ctx2 = document.getElementById('bookingBarChart').getContext('2d');
var barChart = new Chart(ctx2, {
    type: 'bar',
    data: {
        labels: <?php echo json_encode($months); ?>,
        datasets: [{
            label: 'Monthly Bookings',
            data: <?php echo json_encode($monthlyCounts); ?>,
            backgroundColor: 'rgba(255, 193, 7, 0.8)',
            borderColor: 'rgba(255, 193, 7, 1)',
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// PIE CHART (Booking Status)
var ctx3 = document.getElementById('bookingPieChart').getContext('2d');
var pieChart = new Chart(ctx3, {
    type: 'pie',
    data: {
        labels: ['Confirmed', 'Cancelled', 'Pending'],
        datasets: [{
            data: [<?php echo $confirmed; ?>, <?php echo $cancelled; ?>, <?php echo $pending; ?>],
            backgroundColor: [
                'rgba(40, 167, 69, 0.8)',
                'rgba(220, 53, 69, 0.8)',
                'rgba(255, 193, 7, 0.8)'
            ],
            borderColor: [
                'rgba(40, 167, 69, 1)',
                'rgba(220, 53, 69, 1)',
                'rgba(255, 193, 7, 1)'
            ],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Function to handle internet connection status - ramos
function handleInternetConnection() {
    const overlay = document.getElementById('no-internet-overlay');
    if (!navigator.onLine) {
        overlay.style.display = 'flex';
        Swal.fire({
            icon: 'error',
            title: 'No Internet Connection',
            text: 'Please check your internet connection.',
            showConfirmButton: false,
            timer: 3000,
            toast: true,
            position: 'top-end'
        });

    } else {
        overlay.style.display = 'none';
    }
  }
// Event listeners for online/offline status
window.addEventListener('offline', handleInternetConnection);
window.addEventListener('online', handleInternetConnection);

// Initial check
handleInternetConnection();

// Fetch payment statistics for Payment Method Distribution chart
$.ajax({
  url: 'get-payment-stats.php',
  type: 'GET',
  dataType: 'json',
  success: function(response) {
    if(response.success) {
      // Update payment statistics info boxes
      $('#gcashCount').text(response.gcash_count);
      $('#manualCount').text(response.manual_count);

      // Calculate percentages for progress bars
      const total = response.gcash_count + response.manual_count;
      const gcashPercent = total > 0 ? (response.gcash_count / total * 100) : 0;
      const manualPercent = total > 0 ? (response.manual_count / total * 100) : 0;

      $('#gcashProgress').css('width', gcashPercent + '%');
      $('#manualProgress').css('width', manualPercent + '%');

      // Create payment method distribution line chart
      const paymentCtx = document.getElementById('paymentMethodChart').getContext('2d');
      new Chart(paymentCtx, {
        type: 'line',
        data: {
          labels: response.months,
          datasets: [
            {
              label: 'By GCash',
              data: response.gcash_monthly,
              backgroundColor: 'rgba(23, 162, 184, 0.2)',
              borderColor: 'rgba(23, 162, 184, 1)',
              borderWidth: 2,
              tension: 0.4
            },
            {
              label: 'By Manual Payment',
              data: response.manual_monthly,
              backgroundColor: 'rgba(40, 167, 69, 0.2)',
              borderColor: 'rgba(40, 167, 69, 1)',
              borderWidth: 2,
              tension: 0.4
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                stepSize: 1
              }
            }
          },
          plugins: {
            legend: {
              position: 'bottom'
            },
            title: {
              display: true,
              text: 'Monthly Bookings by Payment Type'
            }
          }
        }
      });
    }
  },
  error: function() {
    console.error('Error fetching payment statistics');
  }
});
</script>
</body>
</html>
