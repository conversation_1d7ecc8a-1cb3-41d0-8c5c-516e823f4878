<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
session_start();

// Include database configuration
include('includes/config.php');

// Check database connection
if (!$con) {
    die('Database Connection Error: ' . mysqli_connect_error());
}

// Validate admin session - redirect to login if not logged in
if(strlen($_SESSION['aid']) == 0) {
    header('location:/Online Booking Reservation System/admin-system/login/admin-login.php');
    exit; // Add exit after redirect for security
} else {
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Accepted Bookings | Admin Dashboard</title>
  <!-- Bootstrap CSS (CDN) -->
  <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
  <!-- FontAwesome (optional, CDN) -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
  <!-- AdminLTE CSS (keep local if available) -->
  <link rel="stylesheet" href="dist/css/adminlte.min.css">
  <!-- DataTables -->
  <link rel="stylesheet" href="plugins/datatables-bs4/css/dataTables.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-responsive/css/responsive.bootstrap4.min.css">
  <link rel="stylesheet" href="plugins/datatables-buttons/css/buttons.bootstrap4.min.css">
  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
</head>
<body class="hold-transition sidebar-mini">
<div class="wrapper">
  <!-- Navbar -->
<?php include_once("includes/navbar.php");?>
  <!-- /.navbar -->

<?php include_once("includes/sidebar.php");?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1>Accepted Bookings</h1>
          </div>
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="dashboard.php">Home</a></li>
              <li class="breadcrumb-item active">Accepted Bookings</li>
            </ol>
          </div>
        </div>
      </div><!-- /.container-fluid -->
    </section>

    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <div class="card">
              <div class="card-header bg-success">
                <h3 class="card-title"><i class="fas fa-check-circle"></i> Accepted Bookings List</h3>
                <div class="card-tools">
                  <button type="button" class="btn btn-tool" onclick="document.querySelector('.card-body').classList.toggle('collapse')">
                    <i class="fas fa-minus"></i>
                  </button>
                  <button type="button" class="btn btn-tool" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt"></i>
                  </button>
                </div>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                <table id="example1" class="table table-bordered table-striped">
                    <thead class="thead-dark">
                  <tr>
                    <th>#</th>
                    <th>Reference No</th>
                    <th>Customer Name</th>
                    <th>Email</th>
                    <th>Contact No</th>
                    <th>Boat</th>
                    <th>Destination</th>
                    <th>Date</th>
                    <th>Time</th>
                    <th>Total Amount</th>
                    <th>Status</th>
                    <th>Action</th>
                  </tr>
                  </thead>
                  <tbody>
                  <?php
                    // First update any bookings with future dates (2025)
                    $update_sql = "UPDATE bookings
                    SET
                        start_date = DATE_SUB(NOW(), INTERVAL 5 DAY),
                        end_date = DATE_SUB(NOW(), INTERVAL 4 DAY),
                        booking_time = DATE_SUB(NOW(), INTERVAL 7 DAY),
                        created_at = DATE_SUB(NOW(), INTERVAL 8 DAY)
                    WHERE
                        booking_status IN ('accepted', 'confirmed') AND YEAR(created_at) = 2025";
                    $con->query($update_sql);

                    // FIXED: Query to fetch all accepted and confirmed bookings
                    // First check which enum values are allowed in the database
                    $check_enum_query = "SHOW COLUMNS FROM bookings LIKE 'booking_status'";
                    $check_enum_result = $con->query($check_enum_query);
                    $enum_values = [];
                    $status_condition = "";

                    if ($check_enum_result && $check_enum_result->num_rows > 0) {
                        $row = $check_enum_result->fetch_assoc();
                        $enum_type = $row['Type'];

                        // Extract enum values using regex
                        if (preg_match("/^enum\((.*)\)$/", $enum_type, $matches)) {
                            $enum_str = $matches[1];
                            $enum_values = str_getcsv($enum_str, ',', "'");

                            // Log the allowed enum values
                            error_log("Allowed booking status values: " . implode(", ", $enum_values));
                        }
                    }

                    // Build the status condition based on available enum values
                    if (in_array('accepted', $enum_values) && in_array('confirmed', $enum_values)) {
                        $status_condition = "b.booking_status IN ('accepted', 'confirmed')";
                    } else if (in_array('accepted', $enum_values)) {
                        $status_condition = "b.booking_status = 'accepted'";
                    } else if (in_array('confirmed', $enum_values)) {
                        $status_condition = "b.booking_status = 'confirmed'";
                    } else {
                        // Fallback to a condition that will work with any status
                        $status_condition = "(b.booking_status = 'confirmed' OR b.booking_status = 'accepted')";
                    }

                    // Using direct SQL for maximum compatibility
                    $sql = "SELECT
                        b.booking_id, b.booking_code, b.first_name, b.last_name,
                        b.email, b.contact_number, b.start_date, b.booking_time,
                        b.total as total, b.booking_status, 'Assigned by Tourism Office' as boat_name,
                        COALESCE(b.tour_destination, 'Not set') as destination_name
                    FROM
                        bookings b
                    WHERE
                        $status_condition
                    ORDER BY
                        b.created_at DESC";

                    error_log("FINAL EMERGENCY FIX: Using direct SQL query for accepted bookings");
                    $result = $con->query($sql);

                    // Check if query execution was successful
                    if (!$result) {
                        echo '<tr><td colspan="12" class="text-center text-danger">
                            <i class="fas fa-exclamation-triangle"></i> Error executing query: ' . $con->error .
                        '</td></tr>';
                    } else {
                        // Check if we have any bookings
                        if ($result->num_rows > 0) {
                            $counter = 1; // Counter for row numbering

                            // Loop through each booking
                            while ($row = $result->fetch_assoc()) {
                                // Determine status display formatting
                                $status = $row['booking_status'];
                                // FIXED: Handle both 'accepted' and 'confirmed' statuses
                                if ($status == 'confirmed') {
                                    $status_class = 'bg-success';
                                    $status_text = 'Confirmed';
                                } else if ($status == 'accepted') {
                                    $status_class = 'bg-info';
                                    $status_text = 'Accepted';
                                } else {
                                    // Fallback for any other status that might appear here
                                    $status_class = 'bg-secondary';
                                    $status_text = ucfirst($status);
                                }
                                ?>
                                <tr>
                                    <!-- Row number counter -->
                                    <td><?php echo $counter++; ?></td>

                                    <!-- Booking reference code -->
                                    <td><?php echo htmlspecialchars($row['booking_code']); ?></td>

                                    <!-- Customer full name -->
                                    <td><?php echo htmlspecialchars($row['first_name'].' '.$row['last_name']); ?></td>

                                    <!-- Contact information -->
                                    <td><?php echo htmlspecialchars($row['email']); ?></td>
                                    <td><?php echo htmlspecialchars($row['contact_number']); ?></td>

                                    <!-- Booking details -->
                                    <td><?php echo htmlspecialchars($row['boat_name']); ?></td>
                                    <td><?php echo htmlspecialchars($row['destination_name']); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($row['start_date'])); ?></td>
                                    <td><?php echo date('H:i', strtotime($row['booking_time'])); ?></td>

                                    <!-- Financial information -->
                                    <td>₱ <?php echo number_format($row['total'], 2); ?></td>

                                    <!-- Status badge -->
                                    <td><span class="badge <?php echo $status_class; ?>"><?php echo $status_text; ?></span></td>

                                    <!-- Action buttons -->
                                    <td>
                                        <div class="btn-group" role="group">
                                            <!-- View details button -->
                                            <button class="btn btn-info btn-sm view-btn"
                                                    data-id="<?php echo $row['booking_id']; ?>"
                                                    title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </button>

                                            <!-- Edit booking button -->
                                            <button class="btn btn-warning btn-sm edit-btn"
                                                    data-id="<?php echo $row['booking_id']; ?>"
                                                    title="Edit Booking">
                                                <i class="fas fa-edit"></i>
                                            </button>

                                            <!-- Delete booking button -->
                                            <button class="btn btn-danger btn-sm delete-btn"
                                                    data-id="<?php echo $row['booking_id']; ?>"
                                                    title="Delete Booking">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php
                            }
                        } else {
                            echo '<tr><td colspan="12" class="text-center"><i class="fas fa-info-circle"></i> No accepted bookings found.</td></tr>';
                        }
                    }
                    ?>
                  </tbody>
                </table>
              </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- Action Modal -->
  <div class="modal fade" id="actionModal" tabindex="-1" role="dialog" aria-labelledby="actionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="actionModalLabel">Confirm Action</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body" id="modalMessage"></div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" id="modalOkBtn">Confirm</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Booking Details Modal -->
  <div class="modal fade" id="bookingDetailsModal" tabindex="-1" role="dialog" aria-labelledby="bookingDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header bg-info">
          <h5 class="modal-title text-white" id="bookingDetailsModalLabel">
            <i class="fas fa-info-circle"></i> Booking Details
          </h5>
          <div>
            <button type="button" class="btn btn-light btn-sm mr-2" id="printBookingDetails">
              <i class="fas fa-print"></i> Print
            </button>
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        </div>
        <div class="modal-body">
          <div class="table-responsive">
            <table class="table table-bordered table-striped" id="bookingDetailsTable">
              <tr><th>First Name:</th><td id="viewFirstName"></td></tr>
              <tr><th>Last Name:</th><td id="viewLastName"></td></tr>
              <tr><th>Age:</th><td id="viewAge"></td></tr>
              <tr><th>Sex:</th><td id="viewSex"></td></tr>
              <tr><th>Contact Number:</th><td id="viewContactNumber"></td></tr>
              <tr><th>Email:</th><td id="viewEmail"></td></tr>
              <tr><th>Address:</th><td id="viewAddress"></td></tr>
              <tr><th>Emergency Contact Name:</th><td id="viewEmergencyName"></td></tr>
              <tr><th>Emergency Contact Number:</th><td id="viewEmergencyNumber"></td></tr>
              <tr><th>Tour Destination:</th><td id="viewDestination"></td></tr>
              <tr><th>Drop-off Location:</th><td id="viewDropOffLocation"></td></tr>
              <tr><th>Number of Pax:</th><td id="viewNoOfPax"></td></tr>
              <tr><th>Start Date:</th><td id="viewStartDate"></td></tr>
              <tr><th>End Date:</th><td id="viewEndDate"></td></tr>
              <tr><th>Booking Time:</th><td id="viewBookingTime"></td></tr>
              <tr><th>Selected Boat:</th><td id="viewBoat"></td></tr>
              <tr><th>Boat Price:</th><td id="viewBoatPrice"></td></tr>
              <tr><th>Booking Code:</th><td id="viewBookingCode"></td></tr>

              <tr><th>Environmental Fee:</th><td id="viewEnvironmentalFee"></td></tr>
              <tr><th>Payment Method:</th><td id="viewPaymentMethod"></td></tr>
              <tr><th>Total Amount:</th><td id="viewTotal"></td></tr>
            </table>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Boat Modal -->
  <div class="modal fade" id="addBoatModal" tabindex="-1" role="dialog" aria-labelledby="addBoatModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
      <div class="modal-content">
        <div class="modal-header bg-success">
          <h5 class="modal-title text-white" id="addBoatModalLabel">
            <i class="fas fa-ship"></i> Add New Boat
          </h5>
          <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <form id="addBoatForm">
            <div class="form-group">
              <label for="boatName">Boat Name</label>
              <input type="text" class="form-control" id="boatName" name="name" required>
            </div>
            <div class="form-group">
              <label for="boatType">Boat Type</label>
              <select class="form-control" id="boatType" name="type" required>
                <option value="">Select Boat Type</option>
                <option value="small">Small Boat</option>
                <option value="medium">Medium Boat</option>
                <option value="large">Large Boat</option>
                <option value="special">Special Boat</option>
              </select>
            </div>
            <div class="form-group">
              <label for="boatCapacity">Capacity (persons)</label>
              <input type="number" class="form-control" id="boatCapacity" name="capacity" min="1" max="50" required>
            </div>
            <div class="form-group">
              <label for="boatPrice">Price Per Day (P)</label>
              <input type="number" class="form-control" id="boatPrice" name="price_per_day" min="0" step="0.01" required>
            </div>
            <div class="form-group">
              <label for="boatDescription">Description</label>
              <textarea class="form-control" id="boatDescription" name="description" rows="3"></textarea>
            </div>
            <div class="form-group">
              <label for="boatStatus">Status</label>
              <select class="form-control" id="boatStatus" name="status" required>
                <option value="available">Available</option>
                <option value="maintenance">Under Maintenance</option>
                <option value="reserved">Reserved</option>
              </select>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-success" id="saveBoat">Save Boat</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Edit Booking Modal -->
  <div class="modal fade" id="editBookingModal" tabindex="-1" role="dialog" aria-labelledby="editBookingModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
      <div class="modal-content">
        <div class="modal-header bg-primary">
          <h5 class="modal-title text-white" id="editBookingModalLabel">Edit Booking</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <form id="editBookingForm">
            <input type="hidden" name="booking_id" id="editBookingId">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Booking Code</label>
                                <input type="text" class="form-control" id="editBookingCode" readonly>
              </div>
                            <div class="form-group">
                                <label>Customer Name</label>
                                <input type="text" class="form-control" id="editCustomerName" readonly>
              </div>
                            <div class="form-group">
                                <label>Email</label>
                                <input type="email" class="form-control" id="editEmail" readonly>
            </div>
                            <div class="form-group">
                                <label>Contact Number</label>
                                <input type="text" class="form-control" id="editContactNumber" readonly>
              </div>
                            <div class="form-group">
                                <label>Address</label>
                                <input type="text" class="form-control" id="editAddress" name="address">
              </div>
                            <div class="form-group">
                                <label>Age</label>
                                <input type="number" class="form-control" id="editAge" name="age" min="1" max="120">
              </div>
                            <div class="form-group">
                                <label>Sex</label>
                                <select class="form-control" id="editSex" name="sex">
                                    <option value="Male">Male</option>
                                    <option value="Female">Female</option>
                                </select>
              </div>
                            <div class="form-group">
                                <label>Boat</label>
                                <div class="input-group">
                                  <select class="form-control" id="editBoat" name="boat_id" required>
                                      <option value="">Select Boat</option>
                                  </select>
                                  <div class="input-group-append">
                                    <button class="btn btn-success" type="button" id="addNewBoatBtn">
                                      <i class="fas fa-plus"></i>
                                    </button>
                                  </div>
                                </div>
              </div>
              </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Destination</label>
                                <select class="form-control" id="editDestination" name="destination_id" required>
                                    <option value="">Select Destination</option>
                                </select>
            </div>
                            <div class="form-group">
                                <label>Start Date</label>
                <input type="date" class="form-control" id="editStartDate" name="start_date" required>
              </div>
                            <div class="form-group">
                                <label>End Date</label>
                <input type="date" class="form-control" id="editEndDate" name="end_date" required>
              </div>
                            <div class="form-group">
                                <label>Booking Time</label>
                                <input type="time" class="form-control" id="editBookingTime" name="booking_time" required>
            </div>
            <div class="form-group">
                                <label>Total Amount</label>
                                <input type="number" class="form-control" id="editTotal" name="total" required>
                            </div>
                        </div>
            </div>
                    <div class="row">
                        <div class="col-12">

            <div class="form-group">
                                <label>Emergency Contact Name</label>
                                <input type="text" class="form-control" id="editEmergencyName" name="emergency_name">
            </div>
            <div class="form-group">
                                <label>Emergency Contact Number</label>
                                <input type="text" class="form-control" id="editEmergencyNumber" name="emergency_number">
            </div>
            <div class="form-group">
                                <label>Drop-off Location</label>
                                <input type="text" class="form-control" id="editDropOffLocation" name="drop_off_location">
            </div>
            <div class="form-group">
                                <label>Number of Pax</label>
                                <input type="number" class="form-control" id="editNoOfPax" name="no_of_pax" required>
            </div>
            <div class="form-group">
                                <label>Environmental Fee</label>
                                <input type="number" step="0.01" class="form-control" id="editEnvironmentalFee" name="environmental_fee">
            </div>
            <div class="form-group">
                                <label>Payment Method</label>
                                <input type="text" class="form-control" id="editPaymentMethod" name="payment_method">
            </div>

            <div class="form-group">
                                <label>Booking Status</label>
                                <select class="form-control" id="editStatus" name="booking_status">
                                    <option value="pending">Pending</option>
                                    <option value="confirmed">Confirmed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
            </div>
      </div>
    </div>
                </form>
  </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveEdit">Save Changes</button>
        </div>
      </div>
    </div>
  </div>

<?php include_once('includes/footer.php');?>

  <!-- Control Sidebar -->
  <aside class="control-sidebar control-sidebar-dark">
    <!-- Control sidebar content goes here -->
  </aside>
  <!-- /.control-sidebar -->
</div>
<!-- ./wrapper -->

<!-- Custom JavaScript Dependencies -->
<script src="../../assets/js/frameworks/dom-library.min.js"></script>
<script src="../../assets/js/frameworks/ui-framework.min.js"></script>
<!-- Custom Admin Core -->
<script src="../../assets/js/admin/admin-core.min.js"></script>
<!-- Custom Data Tables -->
<script src="../../assets/js/components/table-manager.min.js"></script>
<script src="plugins/datatables-bs4/js/dataTables.bootstrap4.min.js"></script>
<script src="plugins/datatables-responsive/js/dataTables.responsive.min.js"></script>
<script src="plugins/datatables-responsive/js/responsive.bootstrap4.min.js"></script>
<script src="plugins/datatables-buttons/js/dataTables.buttons.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.bootstrap4.min.js"></script>
<script src="plugins/jszip/jszip.min.js"></script>
<script src="plugins/pdfmake/pdfmake.min.js"></script>
<script src="plugins/pdfmake/vfs_fonts.js"></script>
<script src="plugins/datatables-buttons/js/buttons.html5.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.print.min.js"></script>
<script src="plugins/datatables-buttons/js/buttons.colVis.min.js"></script>
<!-- AdminLTE for demo purposes -->
<script src="dist/js/demo.js"></script>
<!-- Page specific script -->

//
<script>
function loadBoatsAndDestinations(callback) {
    // Track completion of both AJAX requests
    var boatsLoaded = false;
    var destinationsLoaded = false;

    // Function to check if both requests are complete
    function checkCompletion() {
        if (boatsLoaded && destinationsLoaded && typeof callback === 'function') {
            callback();
        }
    }

    // Load boats data
    $.ajax({
        url: 'get-options.php',
        type: 'GET',
        data: { type: 'boats' },
        dataType: 'html',
        cache: false,
        success: function(response) {
            // Check if we got a valid response
            if (response.length > 0) {
                // Check if response contains actual option elements
                var optionCount = (response.match(/<option/g) || []).length;

                if (optionCount > 0) {
                    // Add options to the dropdown
                    $('#editBoat').html('<option value="">Select Boat</option>' + response);
                } else {
                    // No options found in response
                    $('#editBoat').html('<option value="">No boat options found</option>');
                }
            } else {
                // Empty response
                $('#editBoat').html('<option value="">No boats available</option>');
            }

            // Mark boats as loaded and check if everything is complete
            boatsLoaded = true;
            checkCompletion();
        },
        error: function(xhr, status, error) {
            // Handle error
            $('#editBoat').html('<option value="">Error loading boats</option>');
            boatsLoaded = true;
            checkCompletion();
        }
    });

    // Load destinations data
    $.ajax({
        url: 'get-options.php',
        type: 'GET',
        data: { type: 'destinations' },
        dataType: 'html',
        cache: false,
        success: function(response) {
            // Check if we got a valid response
            if (response.length > 0) {
                // Add options to the dropdown
                $('#editDestination').html('<option value="">Select Destination</option>' + response);
            } else {
                // Empty response
                $('#editDestination').html('<option value="">No destinations available</option>');
            }

            // Mark destinations as loaded and check if everything is complete
            destinationsLoaded = true;
            checkCompletion();
        },
        error: function(xhr, status, error) {
            // Handle error
            $('#editDestination').html('<option value="">Error loading destinations</option>');
            destinationsLoaded = true;
            checkCompletion();
        }
    });
}

$(function() {
    // Initialize page - load data when the page loads
    loadBoatsAndDestinations(function() {
        // This function runs after both boats and destinations are loaded
    });

    /**
     * Handle Add New Boat button click
     * Opens the modal for adding a new boat
     */
    $('#addNewBoatBtn').on('click', function() {
        // Reset the form to clear previous values
        $('#addBoatForm')[0].reset();

        // Show the modal
        $('#addBoatModal').modal('show');
    });

    /**
     * Handle Save Boat button click
     * Validates and submits the new boat form
     */
    $('#saveBoat').on('click', function() {
        // Get the form element
        var form = $('#addBoatForm');

        // Validate the form
        if (!form[0].checkValidity()) {
            // Mark invalid fields
            form.find(':input').addClass('is-invalid');
            form.find(':input').filter(':valid').removeClass('is-invalid');
            return;
        }

        // Get form data for submission
        var formData = form.serialize();

        // Show loading state on the button
        var saveBtn = $(this);
        var originalText = saveBtn.text();
        saveBtn.html('<i class="fas fa-spinner fa-spin"></i> Saving...');
        saveBtn.prop('disabled', true);

        // Send AJAX request to add the boat
        $.ajax({
            url: 'add-boat.php',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Show success message
                    alert('Boat added successfully!');

                    // Close the modal
                    $('#addBoatModal').modal('hide');

                    // Reload boats dropdown to include the new boat
                    loadBoatsAndDestinations(function() {
                        // Boats and destinations reloaded
                    });
                } else {
                    // Show error message
                    alert('Error: ' + (response.error || 'Failed to add boat'));
                }
            },
            error: function(xhr, status, error) {
                // Handle AJAX error
                alert('Error adding boat. Please try again.');
            },
            complete: function() {
                // Reset button state when request completes
                saveBtn.html(originalText);
                saveBtn.prop('disabled', false);
            }
        });
    });

    /**
     * Initialize DataTable with export options
     * Sets up the bookings table with sorting, filtering, and export functionality
     */
    var table = $('#example1').DataTable({
        // Responsive design - table adapts to screen size
        "responsive": true,

        // Allow changing number of entries shown
        "lengthChange": true,

        // Don't automatically resize columns
        "autoWidth": false,

        // Add export buttons
        "buttons": ["copy", "csv", "excel", "pdf", "print"],

        // Default sort by first column descending
        "order": [[0, "desc"]],

        // Show 10 entries by default
        "pageLength": 10,

        // Customize text labels
        "language": {
            "search": "Search bookings:",
            "lengthMenu": "Show _MENU_ entries",
            "info": "Showing _START_ to _END_ of _TOTAL_ entries",
            "infoEmpty": "No entries found",
            "infoFiltered": "(filtered from _MAX_ total entries)"
        }
    });

    // Add the export buttons to the DataTable wrapper
    table.buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');

    /**
     * View booking details handler
     * Loads and displays detailed information about a booking
     */
    $(document).on('click', '.view-btn', function(e) {
        e.preventDefault();
        var id = $(this).data('id');

        // Helper function to format currency as Peso
        function formatPeso(amount) {
            return '₱ ' + parseFloat(amount).toFixed(2).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }

        // Helper function to format time
        function formatTime(timeString) {
            if (!timeString) return 'Not set';

            // If already in HH:mm format
            if (timeString.match(/^\d{2}:\d{2}$/)) {
                return timeString;
            }

            // Try to parse as date
            var timeDate = new Date(timeString);
            if (!isNaN(timeDate.getTime())) {
                return timeDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            }

            return 'Not set';
        }

        // Load booking details via AJAX
        $.ajax({
            url: 'get-booking-details.php',
            type: 'POST',
            data: { id: id },
            dataType: 'json',
            success: function(response) {
                if(response.success && response.data) {
                    var booking = response.data;

                    // Format booking time
                    var formattedTime = formatTime(booking.booking_time);

                    // Populate the modal with booking details
                    $('#viewFirstName').text(booking.first_name || 'Not set');
                    $('#viewLastName').text(booking.last_name || 'Not set');
                    $('#viewAge').text(booking.age || 'Not set');
                    $('#viewSex').text(booking.sex || 'Not set');
                    $('#viewContactNumber').text(booking.contact_number || 'Not set');
                    $('#viewEmail').text(booking.email || 'Not set');
                    $('#viewAddress').text(booking.address || 'Not set');
                    $('#viewEmergencyName').text(booking.emergency_name || 'Not set');
                    $('#viewEmergencyNumber').text(booking.emergency_number || 'Not set');
                    $('#viewDestination').text(booking.destination_name || 'Not set');
                    $('#viewDropOffLocation').text(booking.drop_off_location || 'Not set');
                    $('#viewNoOfPax').text(booking.no_of_pax || 'Not set');
                    $('#viewStartDate').text(booking.start_date ? new Date(booking.start_date).toLocaleDateString() : 'Not set');
                    $('#viewEndDate').text(booking.end_date ? new Date(booking.end_date).toLocaleDateString() : 'Not set');
                    $('#viewBookingTime').text(formattedTime);
                    $('#viewBoat').text(booking.boat_name || 'Not set');
                    $('#viewBoatPrice').text(booking.price_per_day ? formatPeso(booking.price_per_day) + ' per day' : 'Not set');
                    $('#viewBookingCode').text(booking.booking_code || 'Not set');
                    $('#viewEnvironmentalFee').text(booking.environmental_fee ? formatPeso(booking.environmental_fee) : 'Not set');
                    $('#viewPaymentMethod').text(booking.payment_method ? booking.payment_method.toUpperCase() : 'Not set');
                    $('#viewTotal').text(booking.total ? formatPeso(booking.total) : 'Not set');

                    // Show the modal with booking details
                    $('#bookingDetailsModal').modal('show');
                } else {
                    // Show error if response indicates failure
                    alert('Error loading booking details: ' + (response.error || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                // Handle AJAX error
                alert('Error loading booking details. Please try again.');
            }
        });
    });

    /**
     * Edit booking handler
     * Loads booking data and opens the edit form
     */
    $('.edit-btn').click(function() {
        var id = $(this).data('id');

        // First load boats and destinations, then show modal and populate form
        loadBoatsAndDestinations(function() {
            // After boats and destinations are loaded, get booking details
            $.ajax({
                url: 'get-booking-details.php',
                type: 'POST',
                data: { id: id },
                dataType: 'json',
                success: function(response) {
                    if(response.success && response.data) {
                        var booking = response.data;

                        // Populate customer information fields
                        $('#editBookingId').val(booking.booking_id);
                        $('#editBookingCode').val(booking.booking_code);
                        $('#editCustomerName').val(booking.first_name + ' ' + booking.last_name);
                        $('#editEmail').val(booking.email);
                        $('#editContactNumber').val(booking.contact_number);
                        $('#editAddress').val(booking.address);
                        $('#editAge').val(booking.age);
                        $('#editSex').val(booking.sex);

                        // Populate booking details fields
                        $('#editEmergencyName').val(booking.emergency_name);
                        $('#editEmergencyNumber').val(booking.emergency_number);
                        $('#editDropOffLocation').val(booking.drop_off_location);
                        $('#editNoOfPax').val(booking.no_of_pax);
                        $('#editStartDate').val(booking.start_date);
                        $('#editEndDate').val(booking.end_date);
                        $('#editBookingTime').val(booking.booking_time);

                        // Populate payment information
                        $('#editEnvironmentalFee').val(booking.environmental_fee);
                        $('#editPaymentMethod').val(booking.payment_method);
                        $('#editTotal').val(booking.total);
                        $('#editStatus').val(booking.booking_status);

                        // Set boat and destination values after a short delay
                        // to ensure dropdowns are fully populated
                        setTimeout(function() {
                            $('#editBoat').val(booking.boat_id);
                            $('#editDestination').val(booking.destination_id);

                            // Show the modal after everything is set up
                            $('#editBookingModal').modal('show');
                        }, 500);
                    } else {
                        // Show error if response indicates failure
                        alert('Error loading booking details: ' + (response.error || 'Unknown error'));
                    }
                },
                error: function(xhr, status, error) {
                    // Handle AJAX error
                    alert('Error loading booking details. Please try again.');
                }
            });
        });
    });

    /**
     * Delete booking handler
     * Shows confirmation dialog and handles deletion process
     */
    $('.delete-btn').click(function() {
        var id = $(this).data('id');

        // Show confirmation dialog
        $('#modalMessage').html(
            '<div class="alert alert-warning">' +
                '<i class="fas fa-exclamation-triangle"></i> Are you sure you want to delete this booking?' +
                '<br><small class="text-muted">This action cannot be undone.</small>' +
            '</div>'
        );
        $('#actionModal').modal('show');

        // Handle confirmation button click
        $('#modalOkBtn').off('click').on('click', function() {
            // Send delete request
            $.ajax({
                url: 'delete-booking.php',
                type: 'POST',
                data: { id: id },
                dataType: 'json',
                success: function(response) {
                    if(response.success) {
                        // Show success message
                        $('#modalMessage').html(
                            '<div class="alert alert-success">' +
                                '<i class="fas fa-check-circle"></i> Booking deleted successfully.' +
                            '</div>'
                        );

                        // Change button to close and reload page when clicked
                        $('#modalOkBtn').text('Close').off('click').on('click', function() {
                            window.location.reload();
                        });
                    } else {
                        // Show error message
                        $('#modalMessage').html(
                            '<div class="alert alert-danger">' +
                                '<i class="fas fa-exclamation-triangle"></i> Error: ' +
                                (response.error || 'Failed to delete booking') +
                            '</div>'
                        );
                    }
                },
                error: function() {
                    // Handle AJAX error
                    $('#modalMessage').html(
                        '<div class="alert alert-danger">' +
                            '<i class="fas fa-exclamation-triangle"></i> An error occurred while deleting the booking.' +
                        '</div>'
                    );
                }
            });
        });
    });

    /**
     * Save edited booking handler
     * Submits the edited booking form data to the server
     */
    $('#saveEdit').click(function() {
        // Get form data
        var formData = $('#editBookingForm').serialize();

        // FINAL EMERGENCY FIX: Add destination name to form data
        var destinationId = $('#editDestination').val();
        var destinationName = $('#editDestination option:selected').text();

        // Log the destination info
        console.log('FINAL EMERGENCY FIX: Adding destination info to form data');
        console.log('Destination ID:', destinationId);
        console.log('Destination Name:', destinationName);

        // Add destination name to form data
        formData += '&destination_name=' + encodeURIComponent(destinationName);

        // FINAL EMERGENCY FIX: Add boat name to form data
        var boatId = $('#editBoat').val();
        var boatName = $('#editBoat option:selected').text();

        // Log the boat info
        console.log('FINAL EMERGENCY FIX: Adding boat info to form data');
        console.log('Boat ID:', boatId);
        console.log('Boat Name:', boatName);

        // Add boat name to form data
        formData += '&boat_name=' + encodeURIComponent(boatName);

        // FINAL EMERGENCY FIX: Add customer name to form data
        var customerName = $('#editCustomerName').val();

        // Log the customer info
        console.log('FINAL EMERGENCY FIX: Adding customer info to form data');
        console.log('Customer Name:', customerName);

        // Add customer name to form data
        formData += '&customer_name=' + encodeURIComponent(customerName);

        // Submit form data to update the booking
        $.ajax({
            url: 'update-booking.php',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if(response.success) {
                    // Close modal and show success message
                    $('#editBookingModal').modal('hide');
                    alert('Booking updated successfully');

                    // Reload page to show updated data
                    window.location.reload();
                } else {
                    // Show error message
                    alert(response.error || 'Error updating booking');
                }
            },
            error: function(xhr, status, error) {
                // Try to parse the error response for more details
                try {
                    var errorObj = JSON.parse(xhr.responseText);
                    alert('Error updating booking: ' + (errorObj.error || error));
                } catch(e) {
                    // If parsing fails, show generic error
                    alert('Error updating booking: ' + error);
                }
            }
        });
    });

    /**
     * Print booking details handler
     * Creates a printable version of the booking details
     */
    $('#printBookingDetails').click(function() {
        // Collect all booking data from the modal
        var bookingData = {
            // Customer information
            firstName: $('#viewFirstName').text(),
            lastName: $('#viewLastName').text(),
            age: $('#viewAge').text(),
            sex: $('#viewSex').text(),
            contactNumber: $('#viewContactNumber').text(),
            email: $('#viewEmail').text(),
            address: $('#viewAddress').text(),
            emergencyName: $('#viewEmergencyName').text(),
            emergencyNumber: $('#viewEmergencyNumber').text(),

            // Booking details
            destination: $('#viewDestination').text(),
            dropOffLocation: $('#viewDropOffLocation').text(),
            noOfPax: $('#viewNoOfPax').text(),
            startDate: $('#viewStartDate').text(),
            endDate: $('#viewEndDate').text(),
            bookingTime: $('#viewBookingTime').text(),
            boat: $('#viewBoat').text(),
            boatPrice: $('#viewBoatPrice').text(),
            bookingCode: $('#viewBookingCode').text(),

            // Payment information
            environmentalFee: $('#viewEnvironmentalFee').text(),
            paymentMethod: $('#viewPaymentMethod').text(),
            total: $('#viewTotal').text()
        };

        // Create the print layout HTML
        var printContent =
            '<div id="printSection">' +
                // Header section with logo and title
                '<div class="print-header">' +
                    '<img src="images/timbook-carles-tourism.png" alt="Timbook Carles Tourism">' +
                    '<h2>Booking Details</h2>' +
                    '<p>Carles Tourism</p>' +
                    '<div class="booking-code">Booking Code: ' + bookingData.bookingCode + '</div>' +
                '</div>' +

                // Main content section with booking details
                '<div class="print-content">' +
                    // Customer information
                    '<div class="print-row">' +
                        '<div class="print-label">Name:</div>' +
                        '<div class="print-value">' + bookingData.firstName + ' ' + bookingData.lastName + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Age:</div>' +
                        '<div class="print-value">' + bookingData.age + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Sex:</div>' +
                        '<div class="print-value">' + bookingData.sex + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Contact:</div>' +
                        '<div class="print-value">' + bookingData.contactNumber + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Email:</div>' +
                        '<div class="print-value">' + bookingData.email + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Address:</div>' +
                        '<div class="print-value">' + bookingData.address + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Emergency Name:</div>' +
                        '<div class="print-value">' + bookingData.emergencyName + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Emergency Contact:</div>' +
                        '<div class="print-value">' + bookingData.emergencyNumber + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Destination:</div>' +
                        '<div class="print-value">' + bookingData.destination + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Drop-off Location:</div>' +
                        '<div class="print-value">' + bookingData.dropOffLocation + '</div>' +
                    '</div>' +

                    // Booking details
                    '<div class="print-row">' +
                        '<div class="print-label">No. of Pax:</div>' +
                        '<div class="print-value">' + bookingData.noOfPax + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Start Date:</div>' +
                        '<div class="print-value">' + bookingData.startDate + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">End Date:</div>' +
                        '<div class="print-value">' + bookingData.endDate + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Time:</div>' +
                        '<div class="print-value">' + bookingData.bookingTime + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Boat:</div>' +
                        '<div class="print-value">' + bookingData.boat + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Boat Price:</div>' +
                        '<div class="print-value">' + bookingData.boatPrice + '</div>' +
                    '</div>' +

                    // Payment information
                    '<div class="print-row">' +
                        '<div class="print-label">Env. Fee:</div>' +
                        '<div class="print-value">' + bookingData.environmentalFee + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Payment:</div>' +
                        '<div class="print-value">' + bookingData.paymentMethod + '</div>' +
                    '</div>' +
                    '<div class="print-row">' +
                        '<div class="print-label">Total:</div>' +
                        '<div class="print-value">' + bookingData.total + '</div>' +
                    '</div>' +
                '</div>' +

                // Footer section
                '<div class="print-footer">' +
                    '<p>Thank you for choosing Carles Tourism!</p>' +
                    '<p>This is a computer-generated document.</p>' +
                '</div>' +
            '</div>';

        // Save current page content
        var originalContents = document.body.innerHTML;

        // Replace with print content
        document.body.innerHTML = printContent;

        // Trigger browser print dialog
        window.print();

        // Restore original content
        document.body.innerHTML = originalContents;

        // Reinitialize DataTable
        $('#example1').DataTable();
    });
});
  </script>

<!-- Print styles for booking details -->
<style>
/* Print-specific styles - only apply when printing */
@media print {
    /* Hide all page elements by default */
    body * {
        visibility: hidden;
    }

    /* Show only the print section and its children */
    #printSection, #printSection * {
        visibility: visible;
    }

    /* Position the print section to fill the page */
    #printSection {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        padding: 20px;
    }

    /* Hide modal elements that shouldn't appear in print */
    .modal-footer, .modal-header {
        display: none !important;
    }

    /* Header section styling */
    .print-header {
        text-align: center;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #000;
    }

    /* Logo styling */
    .print-header img {
        height: 60px;
        margin-bottom: 10px;
    }

    /* Header text styling */
    .print-header h2 {
        margin: 5px 0;
        font-size: 20px;
    }

    .print-header p {
        margin: 5px 0;
        font-size: 14px;
    }

    /* Main content area styling */
    .print-content {
        padding: 10px;
        font-size: 13px;
    }

    /* Row styling for each data item */
    .print-row {
        margin-bottom: 5px;
        padding: 3px 0;
    }

    /* Label styling (left column) */
    .print-label {
        font-weight: bold;
        display: inline-block;
        width: 150px;
    }

    /* Value styling (right column) */
    .print-value {
        display: inline-block;
    }

    /* Footer section styling */
    .print-footer {
        text-align: center;
        margin-top: 20px;
        padding-top: 10px;
        border-top: 1px solid #000;
        font-size: 12px;
    }

    /* Booking code display styling */
    .booking-code {
        font-size: 16px;
        font-weight: bold;
        margin: 5px 0;
    }
}
</style>
</body>
</html>
<?php } ?>