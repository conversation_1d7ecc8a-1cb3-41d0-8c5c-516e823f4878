<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Loading - Timbo-ok <PERSON><PERSON> Tourism</title>
    <link rel="stylesheet" href="../assets/css/icons/icon-system.min.css">
    <style>
        :root {
            --primary: #1e60c8;
            --secondary: #38bdf8;
            --dark-blue: #0c4a6e;
            --light-blue: #e0f2fe;
            --white: #ffffff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .loading-container {
            text-align: center;
            color: var(--white);
        }

        .logo {
            margin-bottom: 1.5rem;
        }

        .logo img {
            height: 80px;
            width: auto;
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            font-weight: 700;
            letter-spacing: 1px;
        }

        .loading-text {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
            opacity: 0.95;
            font-weight: 500;
        }

        .ocean {
            position: relative;
            width: 350px;
            height: 80px; /* Reduced height for smaller waves */
            margin: 0 auto 1.5rem;
            overflow: visible;
        }

        .wave {
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="0.3" d="M0,96L48,106.7C96,117,192,139,288,154.7C384,171,480,181,576,160C672,139,768,85,864,80C960,75,1056,117,1152,133.3C1248,149,1344,139,1392,133.3L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
            position: absolute;
            width: 100%;
            height: 60%; /* Reduced height for smaller waves */
            bottom: 0;
            left: 0;
            animation: wave 3s ease-in-out infinite;
            z-index: 1;
            pointer-events: none;
        }

        .wave:nth-child(2) {
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="0.4" d="M0,32L48,69.3C96,107,192,181,288,181.3C384,181,480,107,576,112C672,117,768,203,864,208C960,213,1056,139,1152,106.7C1248,75,1344,85,1392,90.7L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>');
            animation: wave 3s 0.5s ease-in-out infinite;
            height: 50%; /* Even smaller second wave */
            z-index: 1;
            opacity: 0.5;
            pointer-events: none;
        }

        @keyframes wave {
            0%, 100% {
                transform: translateX(-20px) translateY(0);
            }
            50% {
                transform: translateX(20px) translateY(-5px);
            }
        }

        .boat {
            position: absolute;
            font-size: 3.5rem;
            color: var(--white);
            left: 50%;
            top: 15px; /* Position the boat higher to be more visible above the waves */
            transform: translateX(-50%);
            animation: boatFloat 3s ease-in-out infinite;
            text-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            z-index: 10; /* Increased z-index to ensure boat is above waves */
            pointer-events: none; /* Ensures the boat doesn't interfere with interactions */
            filter: drop-shadow(0 5px 5px rgba(0, 0, 0, 0.1)); /* Add a subtle shadow for depth */
        }

        @keyframes boatFloat {
            0%, 100% {
                transform: translateX(-50%) translateY(0) rotate(0deg);
            }
            25% {
                transform: translateX(-50%) translateY(-5px) rotate(-2deg);
            }
            75% {
                transform: translateX(-50%) translateY(-5px) rotate(2deg);
            }
            50% {
                transform: translateX(-50%) translateY(-8px) rotate(0deg);
            }
        }

        /* Reflection effect for the boat */
        .reflection {
            position: absolute;
            width: 50px;
            height: 8px;
            background: rgba(255, 255, 255, 0.25);
            border-radius: 50%;
            left: 50%;
            bottom: 30px; /* Adjusted to match new boat position */
            transform: translateX(-50%);
            filter: blur(3px);
            animation: reflect 3s ease-in-out infinite;
            z-index: 2;
        }

        @keyframes reflect {
            0%, 100% {
                width: 50px;
                opacity: 0.25;
                transform: translateX(-50%) scaleX(1);
            }
            50% {
                width: 60px;
                opacity: 0.3;
                transform: translateX(-50%) scaleX(1.2);
            }
        }

        .loading-progress {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 30px;
            height: 8px;
            width: 250px;
            margin: 0 auto;
            overflow: hidden;
            position: relative;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) inset;
        }

        .progress-bar {
            background: var(--white);
            height: 100%;
            width: 0;
            border-radius: 30px;
            animation: progress 3s forwards;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
        }

        @keyframes progress {
            0% {
                width: 0%;
            }
            100% {
                width: 100%;
            }
        }

        .loading-footer {
            margin-top: 2rem;
            font-size: 0.8rem;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="loading-container">
        <div class="logo">
            <img src="../img/timbook-carles-tourism.png" alt="Timbo-ok Carles Tourism Logo">
        </div>

        <h1>TIMBO-OK CARLES</h1>
        <p class="loading-text">Naglo-load ang iyong adventure...</p>

        <div class="ocean">
            <div class="wave"></div>
            <div class="wave"></div>
            <div class="boat">
                <i class="fas fa-ship"></i>
            </div>
            <!-- Add a small reflection effect under the boat -->
            <div class="reflection"></div>
        </div>

        <div class="loading-progress">
            <div class="progress-bar"></div>
        </div>

        <p class="loading-footer">© 2025 Timbo-ok Carles Tourism. All rights reserved.</p>
    </div>

    <script>
        // Redirect to main page after loading animation
        setTimeout(function() {
            window.location.href = '/Online Booking Reservation System/php/pages/online-booking.php?loaded=true';
        }, 3000); // 3000ms = 3s to match the progress bar animation
    </script>
</body>
</html>

